# *******.82 ALM-73301 vCenter连接失败

##### 告警解释
FusionSphere OpenStack无法成功连接VMware vCenter服务器。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73301 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>对端地址：vCenter服务器的IP地址 |
| 附加信息 | 详细信息：Failed to connect to VMware vCenter Server，表示连接vCenter服务器失败 |
##### 对系统的影响
影响对接VMware场景下虚拟机网络的正常业务功能。
##### 可能原因
- 外部交换机转发丢包或延时过大。
- 主机网卡或交换机带宽不足。
- 网卡故障。
- VMware vCenter组件没有正常工作。
- VMware vCenter登录帐号密码错误。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 导入环境变量。
5. 使用执行以下命令确认是否通过neutron服务对接vCenter。
openstack service list | grep neutron
命令是否有回显。
- 是，执行6。
- 否，执行10。
6. 确认FusionSphere Openstack与vCenter网络是否连通。
- 是：执行7。
- 否：检查物理网络是否连接和配置正确。确认配置正确后，执行12。
vCenter和FusionSphere OpenStack的主机通过external_om平面相连。确保交换机VLAN已经正确划分，并且主机能通过external_om网络连通vCenter。
7. 在FusionSphere OpenStack安装部署界面，选择“配置 > 资源池管理”。
8. 在“资源池”区域单击VMware资源池下的，查看配置的vCenter对接参数是否正确。
界面如下图所示。
图1 参数配置
9. 设置参数正确后，单击“提交”。
该步骤执行完成后，执行12。
10. 检查物理网络是否连接和配置正确。确认配置正确后，执行cd /home/<USER>
11. 执行sh install_FSP.sh set_vcenter_alarm命令，修改对接vCenter的信息。
按照命令提示输入正确的IP地址、用户名、密码。
12. 等待几分钟后，通过Service OM告警页面查看告警是否自动清除。
- 是，处理完毕。
- 否，执行13。
13. 请联系技术支持工程师协助解决。
##### 参考信息
无。