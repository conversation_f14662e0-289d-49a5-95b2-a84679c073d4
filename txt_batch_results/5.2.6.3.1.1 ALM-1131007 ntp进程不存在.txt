# 5.2.6.3.1.1 ALM-1131007 ntp进程不存在

##### 告警解释
镜像转换节点的ntp进程不存在时，就会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1131007 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源设备名称 | 产生告警信息的设备名称。 |
| 监控系统名称 | 产生告警信息的监控系统名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近一次发生时间。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
影响镜像服务的功能正常运行，同时，可能导致磁盘中的数据随时会被删除。
##### 可能原因
网络故障、ntp进程异常、ntp服务器下电或故障等。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”。
3. 在页面上方的菜单栏，选择“集中告警”，进入“集中告警”页面。
4. 在告警列表中，找到待处理的告警记录，单击“名称”列，弹出“告警详情和处理建议”窗口。
5. 查询以下告警信息：
- 告警ID
- 告警来源设备名称
- 在告警信息的可能原因中获取云服务器的IP地址
- 使用“PuTTY”工具，登录CPT-SRV01或CPT-SRV02节点。
登录地址：5中获取的附加信息的IP地址
默认帐号：sysadmin，默认密码：*****。
- 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su - root
- 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
- 执行以下命令，检查网络是否正常。
ping NTP服务器的IP地址
- 是，执行10。
- 否，请联系技术支持工程师协助解决。
- 执行以下命令，查看ntp进程是否存在。
ps -ef |grep ntp
- 是，执行11。
- 否，执行12。
- 执行以下命令，终止ntp进程。
kill -9 进程号
其中进程号为10中查询到的进程号。
系统会自动启动ntp进程，如过长时间未自动启动进程，执行12启动进程。
- 执行以下命令，启动进程。
service ntp restart
- 等待3分钟，观察告警通知是否消除？
- 是，任务结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。