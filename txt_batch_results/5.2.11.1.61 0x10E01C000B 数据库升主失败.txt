# ********.61 0x10E01C000B 数据库升主失败

##### 告警解释
数据库在HA节点（IP：[Node_Name]）上升主失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C000B | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能导致数据库服务异常。
##### 可能原因
- 数据库同步进度小于规定进度（默认:90%）。
- 数据库同步成功时间小于规定值（默认:10分钟）。
##### 处理步骤
- 可能原因1：数据库同步进度小于规定进度（默认：90%）。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，获取HA主节点和备节点的IP地址。
- 使用PuTTY，登录HA主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行cat /opt/huawei-data-protection/ebackup/conf/db_sync.conf命令打开db_sync.conf文件，查看sync_progress值是否小于allow_sync_progress的值。
- 是，请联系技术支持工程师协助解决。
- 否，执行2。
- 可能原因2：数据库同步成功时间小于规定值（默认：10分钟）。
- 使用PuTTY，登录HA主节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入ROOT用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行cat /opt/huawei-data-protection/ebackup/conf/db_sync.conf命令打开db_sync.conf文件，查看sync_time的值并执行date -d "@sync_time" +"%F %H:%M:%S"将其转换为标准时间，计算与当前时间的差值，是否小于10分钟。
- 是，执行3。
- 否，请联系技术支持工程师协助解决。
- 重启HA主备节点服务。
- 使用PuTTY，依次登录HA主节点和备节点。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 在主备节点上分别执行/opt/huawei-data-protection/ebackup/bin/db_sync_monitor.sh get_status命令查看节点状态。
- 如果两个节点的角色为Primary和Standby，执行3.e。如果两个节点的角色都为Primary，执行3.h。
- 在Standby节点上执行service hcp stop命令，停止Standby节点服务。
- 在Primary节点上执行service hcp restart force命令强制重启Primary节点服务。
- 等待Primary节点上的服务重启完成后，再在Standby节点上执行service hcp start命令，启动Standby节点上的服务，转到4。
- 在主节点上执行cd /opt/huawei-data-protection/ebackup/ha/module/hacom/script命令，进入script目录。
- 在主节点上执行sh status_ha.sh命令，从回显信息中获取“StartTime”字段信息。
- 选择StartTime显示时间距当前时刻较远的节点，在该节点上执行service hcp stop命令停止该节点的服务。
- 选择StartTime显示时间距当前时刻较近的节点，在该节点上执行service hcp restart force命令强制重启该节点的上的服务。
- 在StartTime较近的节点上，执行service hcp restart force命令强制重启该节点的上的服务。
- 等待StartTime较近的节点上的服务重启完成后，再在StartTime较远的节点上执行service hcp start命令，启动该节点上的服务，转到4。
- 查看告警是否消除。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无