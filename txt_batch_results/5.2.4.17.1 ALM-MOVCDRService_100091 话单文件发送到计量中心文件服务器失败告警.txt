# 5.2.4.17.1 ALM-MOVCDRService_100091 话单文件发送到计量中心文件服务器失败告警

##### 告警解释
上传话单文件到计量中心文件服务器失败，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOVCDRService_100091 | 重要 | 通信告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 计量中心文件服务器 | 计量中心文件服务器的地址。 |
##### 对系统的影响
资源的话单文件无法正常上传，导致计量系统无法采集到话单，影响资源的计量统计。
##### 可能原因
- 计量中心文件服务器不能正常使用。
- 计量中心文件服务器空间不足。
##### 处理步骤
1. 检查计量中心文件服务器是否可以正常登录。
- 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“系统管理 > 系统设置 > 系统维护”。
- 在左树中选择“配置信息管理”。
- 单击“数据上报配置”页签。
- 在“分组名称”为“METERSFTP”的所在行，单击“修改”。
- 设置“端口号”、“用户名”、“密码”和“地址”对应的“值”并记录。
- 使用FileZilla工具，根据1.f中记录的信息，检查是否可以正常登录计量中心服务器。
- 是：执行2。
- 否：请联系技术支持工程师协助解决。
2. 检查计量中心文件服务器的/opt/meterfiles/uploads目录，是否有权限创建文件。
- 是：删除创建的文件，执行3。
- 否：请联系技术支持工程师协助解决。
3. 检查计量中心文件服务器的/opt/meterfiles/uploads目录，已用空间是否超过5GB。
- 是：请联系技术支持工程师协助解决。
- 否：执行4。
图1 uploads目录已用空间
4. 等待1个小时后，检查计量中心文件服务器的/opt/meterfiles/uploads目录，是否有新的话单文件上传。
- 是：告警处理完毕。
- 否：请联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无