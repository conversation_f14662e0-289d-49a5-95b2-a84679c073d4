# ********.1 ALM-8000888 dmk服务异常

##### 告警解释
系统每隔60秒检查一次ha状态，如果服务异常，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 8000888 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | dmk进程监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
ha进程异常，对业务稳定性影响严重，需要紧急处理。
##### 可能原因
ha进程异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY工具，通过4获取的IP地址节点登录告警节点。
默认帐号：dmk。
- 如果为华为云Stack 6.5.0之前版本升级至6.5.1，dmk默认密码为*****。
- 如果为华为云Stack 6.5.1或6.5.0新安装场景，dmk默认密码为*****。
6. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
7. 执行以下命令导入环境变量。
source /etc/profile
8. 执行以下命令，检查主备状态是否正常。
haStatus
NODE                     ROLE           PHASE           RESS            VER             START
OM-SRV01(OM-SRV01)       active         Actived         normal          V100R001C01     2019-02-19 09:47:35
OM-SRV02(OM-SRV02)       standby        Deactived       normal          V100R001C01     2019-02-19 09:48:00
--------------------------------------------------------------------------------------------------------
ID    RES                      STAT            RET             TYPE
OM-SRV01(OM-SRV01):      1     exfloatip                Normal          Normal          Single_active
2     nginx                    Normal          Normal          Single_active
3     unicorn                  Normal          Normal          Single_active
OM-SRV02(OM-SRV02):      1     exfloatip                Normal          Stopped         Single_active
2     nginx                    Normal          Stopped         Single_active
3     unicorn                  Normal          Stopped         Single_active
- 是，执行9。
- 否，执行8.a~8.c。
- 执行以下命令，切换到“root”用户。
sudo su - root
- 执行以下命令，启动ha进程。
haStartAll -a
[INFO ] reg ha successful
[INFO ] start ha successful
- 重复执行8，检查主备状态是否正常。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
9. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。