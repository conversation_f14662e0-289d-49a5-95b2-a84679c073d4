# 5.2.11.3.20 0x3230041 上报计量信息失败

##### 告警解释
上报容灾计量信息失败。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230041 | 警告 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 上报失败的Project名称。如果是多个Project，用逗号隔开。 | 计量信息上报失败的项目名称 |
##### 对系统的影响
计量信息上报失败，可能导致计量系统中容灾服务的计量信息不准确。
##### 可能原因
无。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面，在“快速访问”页签单击“eReplication”，登录eReplication。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 选择“资源 > localServer > FusionSphere” 界面，选择 Openstack组件，在“Region”页签中根据Metering列获取计量URL地址。
3. 在HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索“cascading_region”和“cascading_domain”获取到region和domain信息，并替换为链接中https://metering.{cascading_region}.{cascading_domain}:443/的cascading_region和cascading_domain值，与2获取的地址对比是否相同。或在汇总文件《xxx_export_all_CN.xlsm》中搜索“openstack_region”和“openstack_domain”获取到region和domain信息，并替换为链接中https://metering.{openstack_region}.{openstack_domain}:443/的openstack_region和openstack_domain值，与2获取的地址对比是否相同。
- 是，请转6。
- 否，请根据Metering列所在的修改按钮完成计量URL地址的修改。如果告警未清除，请转6。
4. 使用PuTTY，以告警详细信息中的本端链路IP地址登录到本端服务器节点。
默认帐号：DRManager，默认密码：*****。
5. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
6. 执行ping命令检查BCManager与计量服务器（可通过2中的计量URL地址获取）间网络连接是否正常。
- 是，请转7。
- 否，请修复网络连接，然后转7。
7. 待网络连接恢复正常，等待5分钟，系统重试上报后，检查告警是否清除。
- 是，处理结束。
- 否，请转8。
8. 请联系技术支持工程师协助解决。
##### 参考信息
无。