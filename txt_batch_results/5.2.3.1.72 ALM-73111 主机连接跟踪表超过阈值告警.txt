# 5.2.3.1.72 ALM-73111 主机连接跟踪表超过阈值告警

##### 告警解释
主机服务能够接受的连接数是有限的，如果系统连接数高于最大连接数的预设百分比后会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73111 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |
##### 对系统的影响
此告警产生时，系统实时连接数接近最大连接数，而还未到达上限，则对系统无影响。如果连接数继续增加，达到上限，则主机无法继续接受新连接访问，用户数据面和系统控制面会受影响。
##### 可能原因
- 业务超系统规格。
- 系统遭受攻击。
##### 处理步骤
1. 告警是否自动清除。
- 是，任务结束。
- 否，执行2。
2. 确认系统主机和虚拟机数量是否超规格。
- 是，请根据实际情况下电某些主机或者对虚拟机进行迁移或删除等操作，执行完上述操作，告警若仍未消除，执行3。
- 否，执行3。
3. 如果告警仍未恢复，请联系技术支持工程师协助解决。
##### 参考信息
无。