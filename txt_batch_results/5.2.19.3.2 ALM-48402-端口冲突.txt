# ********.2 ALM-48402-端口冲突

##### 告警解释
API网关某些组件启动的时候端口已经被占用。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48402 | 紧急 | 设备告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Component | 告警源组件 |
| 定位信息 | Node | 告警源节点IP地址 |
| 附加信息 | Port | 端口 |
##### 对系统的影响
上报告警的组件端口冲突，无法正常启动，会对该组件相关的业务产生影响。
##### 可能原因
该组件启动需要监听的端口被占用。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警信息。
- Node：表示故障节点IP地址。
- Component：表示故障组件名称。
- Port：表示端口号。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录告警源节点Node。
默认帐号： paas，默认密码：*****。
3. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****。
4. 执行以下命令，防止会话超时退出。
TMOUT=0
5. 执行以下命令，查看占用的端口进程号。
netstat -tlnp | grep Port
6. 执行以下命令，查看该进程号对应的进程信息。
ps -ef | grep 进程号
7. 判断进程信息中是否包含对应的组件名称Component。
- 是 => 11
- 否 => 执行命令结束非法进程：kill 进程号，再执行8。
8. 执行以下命令，切换到组件对应的用户，如表1所示。
su - 用户名
| 表1 组件与用户的对应关系 | 表1 组件与用户的对应关系 | 表1 组件与用户的对应关系 |
| --- | --- | --- |
| 组件 | 用户名 | 命令 |
| gaussdb | apigw_db | su - apigw_db |
| adminportal | apigw_portal | su - apigw_portal |
| apigmgr | apigw_apimgr | su - apigw_apimgr |
| cassandra | apigw_scdb | su - apigw_scdb |
| 其他组件（除gaussdb、adminportal、apigmgr、cassandra以外的组件） | apigateway | su - apigateway |
9. 执行以下命令，重启组件。该重启操作对系统本身无不良影响。
sh /opt/apigateway/Component/shell/restart.sh
显示xxx start successfully，表示服务启动成功。
10. 查看告警是否清除。
- 是 => 处理完毕
- 否 => 11
11. 获取相关日志，并联系技术支持。
- 执行如下命令，切换到root用户。
exit
- 执行如下命令，切换到日志目录。
- 故障组件（Component对应值）为“shubao” => 执行命令：cd /var/log/apigateway/shubao/run
- 故障组件（Component对应值）为其他组件 => 执行命令：cd /var/log/apigateway/Component/runtime
- 下载日志“Component_shell.log”到本地，并联系技术支持。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。