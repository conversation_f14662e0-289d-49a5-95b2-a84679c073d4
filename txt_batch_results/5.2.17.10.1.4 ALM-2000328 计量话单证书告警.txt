# *********.1.4 ALM-2000328 计量话单证书告警

##### 告警解释
计量话单服务证书过期，就会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000328 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
影响接口调用。
##### 可能原因
计量话单节点证书已经过期或者三十天之内过期。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 通过4中获取的节点IP地址判断上报告警的节点。
- 如果是Controller节点，请执行6~17。
- 如果是Agent节点，请执行18~29。
更换Controller节点的证书
6. 使用网络传输工具（例如“WinSCP”工具），将获取的证书上传到Controller节点的“/home/<USER>
7. 使用Putty，以“meteradmin”用户登录Controller节点。
“root”用户的默认密码是“*****”，“meteradmin”用户的默认密码是“*****”，server.crt的默认密码为*****。
8. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
9. 执行以下命令，备份原有证书库文件server.keystore。
mv /home/<USER>/meterticket-controller/resources/keystore/server.keystore /home/<USER>/meterticket-controller/resources/keystore/server.keystore.bak
10. 执行以下命令，生成新的证书库文件server.keystore。
keytool -genkey -alias 证书库别名 -keypass 别名密码 -keyalg 算法 -keysize 密钥长度 -validity 有效期（天） -keystore 指定生成证书的位置和证书名称 -storepass 获取keystore信息的密码 -dname "C=单位的两字母国家代码, ST=州或省份名称, L=城市或区域名称, O=组织名称, OU=组织单位名称, CN=名字与姓氏"
例如，生成server.keystore：
keytool -genkey -alias sdr_jetty -keypass Huadan@szx666 -keyalg RSA -keysize 2048 -validity 3650 -keystore server.keystore -storepass Huadan@szx666 -dname "C=CN, ST=Shaanxi, L=Xi'an, O=Huixin, OU=IT, CN=PRIVATE_CLOUD"
11. 执行以下命令，导入根证书。
keytool -import -v -trustcacerts -alias ca_root -file CA证书名称 -keystore server.keystore
例如导入ca.crt：
keytool -import -v -trustcacerts -alias ca_root -file ca.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
如果回显信息显示keytool命令不存在，则需要执行如下命令导入环境变量。
source /etc/profile
12. 输入证书库密码，按“Enter”。
13. 输入“yes”，按“Enter”。
显示如下信息时，完成CA证书的导入：
Certificate was added to keystore
[Storing server.keystore]
14. 执行以下命令，导入签名后证书。
keytool -import -v -trustcacerts -alias ca_server -file 证书名称 -keystore server.keystore
例如导入server.crt：
keytool -import -v -trustcacerts -alias ca_server -file server.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
15. 输入证书库密码，按“Enter”。
16. 输入“yes”，按“Enter”。
17. 证书替换后，如果告警仍未消除，请联系技术支持工程师协助解决。
更换Agent节点的证书
18. 使用网络传输工具（例如“WinSCP”工具），将获取的证书上传到Agent节点的“/home/<USER>
19. 使用Putty，以“meteradmin”用户登录Agent节点。
“root”用户的默认密码是“*****”，“meteradmin”用户的默认密码是“*****”，server.crt的默认密码为*****。
20. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
21. 执行以下命令，备份原有证书库文件server.keystore。
mv /home/<USER>/meterticket-agent/resources/keystore/server.keystore /home/<USER>/meterticket-agent/resources/keystore/server.keystore.bak
22. 执行以下命令，生成新的证书库文件server.keystore。
keytool -genkey -alias 证书库别名 -keypass 别名密码 -keyalg 算法 -keysize 密钥长度 -validity 有效期（天） -keystore 指定生成证书的位置和证书名称 -storepass 获取keystore信息的密码 -dname "C=单位的两字母国家代码, ST=州或省份名称, L=城市或区域名称, O=组织名称, OU=组织单位名称, CN=名字与姓氏"
例如，生成server.keystore：
keytool -genkey -alias sdr_jetty -keypass Huadan@szx666 -keyalg RSA -keysize 2048 -validity 3650 -keystore server.keystore -storepass Huadan@szx666 -dname "C=CN, ST=Shaanxi, L=Xi'an, O=Huixin, OU=IT, CN=PRIVATE_CLOUD"
23. 执行以下命令，导入根证书。
keytool -import -v -trustcacerts -alias ca_root -file CA证书名称 -keystore server.keystore
例如导入ca.crt：
keytool -import -v -trustcacerts -alias ca_root -file ca.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
如果回显信息显示keytool命令不存在，则需要执行如下命令导入环境变量。
source /etc/profile
24. 输入证书库密码，按“Enter”。
25. 输入“yes”，按“Enter”。
显示如下信息时，完成CA证书的导入：
Certificate was added to keystore
[Storing server.keystore]
26. 执行以下命令，导入签名后证书。
keytool -import -v -trustcacerts -alias ca_server -file 证书名称 -keystore server.keystore
例如导入server.crt：
keytool -import -v -trustcacerts -alias ca_server -file server.crt -keystore server.keystore
显示如下信息：
Enter keystore password:
27. 输入证书库密码，按“Enter”。
28. 输入“yes”，按“Enter”。
29. 证书替换后，如果告警仍未消除，请联系技术支持工程师协助解决。
