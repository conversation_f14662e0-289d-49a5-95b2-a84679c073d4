# ********.1 ALM-2000722 taskcenter服务异常

告警解释
任务中心服务健康检查异常，就会产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2000722 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
创建云服务器时无法正常显示申请状态。
可能原因
任务中心服务异常。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，登录4中确认的虚拟机。
默认帐号：apitask，默认密码：*****。
6. 执行以下命令，查看任务中心进程是否正常且只存在一个。
ps -ef | grep taskcenter | grep -v grep
回显类似如下，所示则表示进程正常。
apitask 13773 1 0 11:47 ? 00:00:15 /opt/common/jre/bin/java -Dname=taskcenter -classpath ...
- 是，请联系技术支持工程师协助解决。
- 否，执行7。
7. 执行以下命令，重启任务中心节点后观察是否仍有告警。
/opt/taskcenter/taskcenter-service/bin/service-restart.sh
- 是，请联系技术支持工程师协助解决。
- 否，处理完毕。
参考信息
无。