# 5.2.3.1.6 ALM-6017 主机状态异常

##### 告警解释
OpenStack周期（默认为60s）检查所有的主机状态，如果存在状态不正常的主机，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6017 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：告警发送节点的地址。<br>对端地址：产生告警的主机地址。<br>故障原因：产生故障的原因。 |
##### 对系统的影响
该主机不能提供服务。
##### 可能原因
- 硬件故障导致主机无法使用。
- 硬件设备掉电，导致写磁盘文件的IO丢失或者写错扇区。
- 主机磁盘损坏或RAID卡未配置电池。
- 网络规划错误出现异常PXE接入的主机。
- cps-client进程进入D状态。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
6. 查询发出告警所在主机的相关信息。
cps host-show 主机ID
主机ID在告警的详细信息中获取。
- 成功获取信息，执行7。
- 否，执行21。
7. 在获取的主机信息中，查看metadata属性ipmiip的值。
metadata属性信息示例如下：
| metadata           | physical_cpu_num:XX                                                     |
|                    | ipmiip:XX.XX.XX.XX                                                      |
|                    | Product Name:XXX                                                        |
- 若主机ipmiip为空，则按照虚拟机系统卷损坏后无法登录的救援方法章节处理。
- 若主机ipmiip不为空，执行8。
8. 检查主机ipmiip是否为裸金属服务器BMC IP地址。
登录Service OM界面，在“资源 > 裸金属资源 > 裸金属服务器”页面，检查是否存在裸金属服务器BMC IP地址和主机ipmiip一致。
- 是，登录FusionSphere OpenStack安装部署界面，在“概要”页面或“运维 > 扩容”，选择该主机，单击“删除”。然后手动清除告警，处理完毕。
- 否，继续9。
9. 通过以下命令查看故障主机是否存在分区未挂载成功。
- 登录故障主机。
- 使用PuTTY，通过告警主机IP（即附加信息中的对端地址）登录故障主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
- 执行su - root命令切换到root用户。
- 执行以下命令查看故障主机是否存在分区未挂载成功。
cps-env-check --mount-status
- 如果回显如下，则说明分区挂载成功，执行12。
- linux-XJgKSf:~ # cps-env-check --mount-status
- =========== [BEGIN]check device mounting status ========================
- Checking /dev/cpsVG/backup mount on /opt/backup success
- Checking /dev/cpsVG/repo mount on /etc/huawei/fusionsphere/repo success
- Checking /dev/cpsVG/zookeeper mount on /opt/fusionplatform/data/zookeeper success
- Checking /dev/cpsVG/upgrade mount on /opt/fusionplatform/data/upgrade success
- Checking /dev/cpsVG/ceilometer-data mount on /var/ceilometer success
- Checking /dev/cpsVG/image-cache mount on /opt/HUAWEI/image_cache success
- Checking /dev/cpsVG/swift mount on /opt/HUAWEI/swift success
- Checking /dev/cpsVG/rabbitmq mount on /opt/fusionplatform/data/rabbitmq success
- Checking /dev/cpsVG/database mount on /opt/fusionplatform/data/gaussdb_data success
- Checking /dev/cpsVG/image mount on /opt/HUAWEI/image success
- Checking /dev/mapper/cpsVG-bak_rootfs mount on /opt/HUAWEI/bak_rootfs success
=========== [END]check device mounting status ==========================
- 如果回显如下，则说明存在分区挂载失败，记录分区名和挂载目录，执行10。
- linux-XJgKSf:~ # cps-env-check --mount-status
- =========== [BEGIN]check device mounting status ========================
- Checking /dev/cpsVG/backup mount on /opt/backup success
- Checking /dev/cpsVG/repo mount on /etc/huawei/fusionsphere/repo success
- Checking /dev/cpsVG/zookeeper mount on /opt/fusionplatform/data/zookeeper success
- Checking /dev/cpsVG/upgrade mount on /opt/fusionplatform/data/upgrade success
- Checking /dev/cpsVG/ceilometer-data mount on /var/ceilometer success
- Checking /dev/cpsVG/image-cache mount on /opt/HUAWEI/image_cache success
- Checking /dev/cpsVG/swift mount on /opt/HUAWEI/swift success
- Checking /dev/cpsVG/rabbitmq mount on /opt/fusionplatform/data/rabbitmq success
- Checking /dev/cpsVG/database mount on /opt/fusionplatform/data/gaussdb_data success
- Checking /dev/cpsVG/image mount on /opt/HUAWEI/image success
- Checking /dev/mapper/cpsVG-bak_rootfs mount on /opt/HUAWEI/bak_rootfs failed
- =========== [END]check device mounting status ==========================
- 如果回显如下，则说明未返回任何分区挂载信息，请联系技术支持工程师协助解决。
- linux-XJgKSf:~ # cps-env-check --mount-status
- =========== [BEGIN]check device mounting status ========================
=========== [END]check device mounting status ==========================
10. 主机系统正在运行，分区无法挂载。执行命令手动挂载分区，确认是否会挂载失败。其中xxx为分区名，/mnt为主机上的挂载目录。
mount xxx /mnt
- 是，如下回显，则表示分区文件系统已损坏，执行11进行修复。
- 否，执行12。
11. 执行命令修复文件系统。
- 执行以下命令查询文件系统类型。
df -T
linux-XJgKSf:~ # df -T
Filesystem                         Type     1K-blocks    Used Available Use% Mounted on
/dev/mapper/cpsVG-rootfs           ext4       8191416 4159476   3596128  54% /
devtmpfs                           devtmpfs  65828552       0  65828552   0% /dev
tmpfs                              tmpfs     65837592      92  65837500   1% /dev/shm
tmpfs                              tmpfs     65837592   83800  65753792   1% /run
tmpfs                              tmpfs     65837592       0  65837592   0% /sys/fs/cgroup
/dev/mapper/cpsVG-data             ext4        499656    6012    456948   2% /opt/fusionplatform/data
/dev/mapper/cpsVG-fsp              ext4        999320   16516    913992   2% /home/<USER>
/dev/sda1                          ext4        480660   73043    378280  17% /boot
/dev/mapper/cpsVG-log              ext4      20511312  279076  19167276   2% /var/log
回显如上所示，则表明示例分区使用ext4文件系统类型。
- 执行以下命令修复文件系统。
fsck.ext4 xxx
xxx为无法挂载的分区。若分区使用ext4以外的其他文件系统类型，使用fsck的其他文件系统修复命令，如ext3文件系统使用fsck.ext3。
- 如果自动修复成功，执行12。
- 否则，请联系技术支持工程师协助解决。
12. 检查主机网络是否故障。
- 执行以下命令，获取告警所在主机ID对应的manageip。
cps host-list
- 执行以下命令，在当前登录的首节点通过ping命令查看是能否和manageip互通。
ping manageip
回显如下：
65BA1CEB-573F-574C-B9F0-EE7F9AFC6ECE:~ # ping **********
PING ********** (**********) 56(84) bytes of data.
From ********** icmp_seq=1 Destination Host Unreachable
From ********** icmp_seq=2 Destination Host Unreachable
From ********** icmp_seq=3 Destination Host Unreachable
From ********** icmp_seq=4 Destination Host Unreachable
From ********** icmp_seq=5 Destination Host Unreachable
From ********** icmp_seq=6 Destination Host Unreachable
From ********** icmp_seq=7 Destination Host Unreachable
From ********** icmp_seq=8 Destination Host Unreachable
^C
--- ********** ping statistics ---
9 packets transmitted, 0 received, +8 errors, 100% packet loss, time 8002ms
若如图所示表明ping不通manageip，请先恢复主机的网络连接。
恢复网络后查看告警是否恢复。
- 是，处理完毕。
- 否，执行13。
13. 执行以下命令，检查cps-client组件进程状态是否进入D状态。
ps -aux|grep cpsclient.py |grep -v grep|awk '{print $8}'
若命令输出显示的状态为D或者D+，则说明cps-client进程进入D状态。
进程进入D状态（不可中断睡眠状态）通常是在等待IO，比如磁盘IO，网络IO，内存不足，其他外设IO等。
- 是，执行16。
- 否，执行14。
14. 通过主机BMC系统，查看主机操作系统是否进入了维护模式。
如图1所示，表示已经进入维护模式。
图1 维护模式
- 是，执行15。
- 否，执行16。
15. 在BMC界面输入root密码登录系统，对最后报错的分区（如图1中的分区/dev/mapper/cpsVG-log）进行手动fsck修复操作。
假设待fsck修复的磁盘分区为/dev/sda5，其修复操作如下所示。
- 使用umount命令将磁盘分区卸载掉。
命令示例如下：
umount /dev/sda5
- 使用fsck命令对磁盘分区进行修复。
命令示例如下：
fsck /dev/sda5
命令示例回显如下所示，再次执行如上命令检查fsck修复的结果是否成功。
# fsck
/dev/sda5 fsck from util-linux 2.19.1 e2fsck 1.41.9 (22-Aug-2009)
/dev/sda5: recovering journal /dev/sda5 contains a file system with errors, check forced.
Pass 1: Checking inodes, blocks, and sizes
Pass 2: Checking directory structure Directory inode 8193, block 0, offset 24: directory corrupted Salvage<y>? yes
Pass 3: Checking directory connectivity
Pass 4: Checking reference counts
Pass 5: Checking group summary information
/dev/sda5: ***** FILE SYSTEM WAS MODIFIED *****
/dev/sda5: 13/131072 files (0.0% non-contiguous), 17206/524112 blocks
# fsck
/dev/sda5 fsck from util-linux 2.19.1 e2fsck 1.41.9 (22-Aug-2009)
/dev/sda5: clean, 13/131072 files, 17206/524112 blocks
16. 重启故障主机。
主机是否可以正常启动。
- 是，执行19。
- 否，执行17。
17. 检查主机硬件是否故障，并参考更换主机及配件相关章节处理。
- 检查主机硬盘是否故障，如果故障，请更换硬盘。
- 检查主机其他硬件是否故障，如果故障，请更换相应部件或直接更换整机。
排除主机硬件故障后，执行18。
18. 重启故障主机。
主机是否可以正常启动。
- 是，执行19。
- 否，执行21。
19. 执行命令检测主机硬盘是否存在坏道。
badblocks -b block_size(Byte) -s disk
“block_size(Byte)”表示检测硬盘时分块的大小，单位为字节；“disk”表示硬盘的设备名，可通过fdisk -l命令查看。
例如：
badblocks -b 4096 -s /dev/sda
如果硬盘存在故障，请参考更换主机及配件相关章节，更换硬盘。
20. 等待故障主机启动后，再等待一分钟，查看告警是否恢复。
- 是，处理完毕。
- 否，执行21。
21. 请联系技术支持工程师协助解决。
##### 参考信息
无。