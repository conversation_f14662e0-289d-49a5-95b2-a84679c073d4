# 5.2.3.1.101 ALM-1200067 DHCP服务不可用

##### 告警解释
当主机上的DHCP组件对于所有或部分网络不能提供DHCP服务时，系统会产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200067 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 区域名：产生告警的区域。<br>云服务：服务类型，固定值为VPC。<br>服务：产生告警的服务名称，默认为DHCP。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 云服务：服务类型，固定值为VPC。<br>服务：产生告警的服务名称，默认为DHCP。<br>主机名：产生告警的主机名称。<br>异常信息：产生告警的原因。<br>分区名：产生告警的分区。 |
##### 对系统的影响
当网络归属的DHCP都无法提供服务后，会影响当前创建虚拟机的通信。已分配IP的虚拟机续组IP失败，在租约到期后会影响虚拟机通信。
##### 可能原因
- DHCP对外提供服务的网络链路不通。
- DHCP名空间端口状态DOWN。
- 缺少可提供服务的dnsmasq进程。
- DHCP agent不正常。
##### 处理步骤
1. 登录告警上报主机。
- 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
- 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
- 执行以下命令，防止系统超时退出。
TMOUT=0
- 参考导入环境变量，选择使用cps鉴权，完成cps环境变量导入。
- 执行cps host-list命令，查看告警定位信息中所报主机对应的管理IP地址。
- 执行以下命令跳转到异常服务所在主机。
su - fsp
ssh fsp@主机管理IP地址
主机管理IP地址从1.e获取。
按照提示输入系统私钥密码，默认私钥密码是“*****”。如果已生成并替换了新的公私钥文件，请输入新私钥密码。
或者直接按“Enter”后按照提示输入fsp用户的密码登录。以fsp用户登录后，通过su - root命令切换到root用户。
- 参考导入环境变量，选择使用keystone v3鉴权，完成openstack环境变量导入。
2. 查看告警附加信息。
- 若附加信息中的“error_info”由“Network network_id failed to ping IP from the DHCP namespace”语句组成，执行3。
- 若附加信息中的“error_info”为“The dnsmasq process(es) of network(s) network_id does(do) not exist or unavailable”，执行4。
- 若附加信息中的“error_info”为“The state of the DHCP agent is abnormal”, 执行5。
- 若附加信息中的“error_info”为“The namespace's port state of network(s) network_id is(are) abnormal”，执行6。
3. 获取2中“error_info”的network_id，执行以下子步骤，处理完成后执行7。
- 检查dhcp-agent状态或者dhcp服务进程是否异常。
- 在告警主机上，查询dhcp-agent是否正常。
执行命令neutron dhcp-agent-list-hosting-net network_id，如下表示dhcp-agent正常。否则，执行3.a.iv。
1DAB63EF-98F8-6FA4-E811-E0270C10E39A:/home/<USER>
+--------------------------------------+--------------------------------------+----------------+-------+
| id                                   | host                                 | admin_state_up | alive |
+--------------------------------------+--------------------------------------+----------------+-------+
| 892eb110-ad9b-4d20-a1a0-f74152fb24f4 | 9056F629-D21D-B211-8607-0018E1C5D866 | True           | :-)   |
| b38fc6cc-6097-4c1d-bba6-1134446a1f12 | 1DAB63EF-98F8-6FA4-E811-E0270C10E39A | True           | :-)   |
+--------------------------------------+--------------------------------------+----------------+-------+
- 执行命令cps template-instance-list --service neutron neutron-dhcp-agent，查询neutron-dhcp-agent服务是否正常，如下表示neutron-dhcp-agent服务正常。否则，执行3.a.iv。
- 1DAB63EF-98F8-6FA4-E811-E0270C10E39A:/home/<USER>
- +----------------+--------------------+--------+--------------------------------------+------------+
- | instanceid     | componenttype      | status | runsonhost                           | omip       |
- +----------------+--------------------+--------+--------------------------------------+------------+
- | agt_0000000002 | neutron-dhcp-agent | active | 8A3E6C7A-3A1D-E811-8334-3CE824851997 | *********** |
- | agt_0000000001 | neutron-dhcp-agent | active | 9056F629-D21D-B211-8607-0018E1C5D866 | *********** |
- | agt_0000000000 | neutron-dhcp-agent | active | 1DAB63EF-98F8-6FA4-E811-E0270C10E39A | *********** |
+----------------+--------------------+--------+--------------------------------------+------------+
由3.a.i和3.a.ii可见，正常情况下，网络绑定的主机是三个控制节点其中的任意两个，状态都是:-)。
- 使用命令ps -ef | grep dnsmasq | grep network_id，查看该网络的dhcp服务进程是否存在。如下表示此进程存在。否则，执行3.a.iv。
- 1DAB63EF-98F8-6FA4-E811-E0270C10E39A:/home/<USER>
opensta+ 42190     1  0 Jul25 ?        00:00:00 dnsmasq --no-hosts --no-resolv --strict-order --except-interface=lo --pid-file=/var/lib/neutron/dhcp/0b36c57d-7cb6-4c0b-9b6c-6e82aff63c9e/pid --dhcp-hostsfile=/var/lib/neutron/dhcp/0b36c57d-7cb6-4c0b-9b6c-6e82aff63c9e/host --addn-hosts=/var/lib/neutron/dhcp/0b36c57d-7cb6-4c0b-9b6c-6e82aff63c9e/addn_hosts --dhcp-optsfile=/var/lib/neutron/dhcp/0b36c57d-7cb6-4c0b-9b6c-6e82aff63c9e/opts --dhcp-leasefile=/var/lib/neutron/dhcp/0b36c57d-7cb6-4c0b-9b6c-6e82aff63c9e/leases --dhcp-match=set:ipxe,175 --dhcp-userclass=set:ipxe,iPXE --enable-ra --ra-param=tapb811d934-31,0,0 --bind-interfaces --interface=tapb811d934-31 --dhcp-range=set:tag0,*********,static,infinite --dhcp-option-force=option:mtu,1500 --dhcp-lease-max=256 --conf-file=/etc/neutron/dnsmasq.conf --domain=openstacklocal --bind-mac-with-ip6
- 如果有dhcp-agent状态异常或者dhcp服务进程异常，分别执行以下两条命令，重启neutron-dhcp-agent组件。
cps host-template-instance-operate --service neutron neutron-dhcp-agent --action stop
cps host-template-instance-operate --service neutron neutron-dhcp-agent --action start
9098B929-D21D-B211-931F-0018E1C5D866:/home/<USER>
+--------------------+--------------------------------------+--------+---------+
| template           | runsonhost                           | action | result  |
+--------------------+--------------------------------------+--------+---------+
| neutron-dhcp-agent | 9098B929-D21D-B211-931F-0018E1C5D866 | stop   | success |
+--------------------+--------------------------------------+--------+---------+
9098B929-D21D-B211-931F-0018E1C5D866:/home/<USER>
+--------------------+--------------------------------------+--------+---------+
| template           | runsonhost                           | action | result  |
+--------------------+--------------------------------------+--------+---------+
| neutron-dhcp-agent | 9098B929-D21D-B211-931F-0018E1C5D866 | start  | success |
+--------------------+--------------------------------------+--------+---------+
- 查询网口是否放通vlan。
- 执行如下命令。
neutron net-show network_id
B4B7CB29-D21D-B211-8F3E-0018E1C5D866:~ # neutron net-show b5e7b481-b07e-444c-a4aa-ddc5a0316252
+---------------------------+--------------------------------------+
| Field                     | Value                                |
+---------------------------+--------------------------------------+
| admin_state_up            | True                                 |
| availability_zone_hints   |                                      |
| availability_zones        |                                      |
| created_at                | 2018-07-24T09:48:10                  |
| description               |                                      |
| id                        | b5e7b481-b07e-444c-a4aa-ddc5a0316252 |
| ipv4_address_scope        |                                      |
| ipv6_address_scope        |                                      |
| mtu                       | 1500                                 |
| name                      | network2198                          |
| port_security_enabled     | True                                 |
| provider:network_type     | vlan                                 |
| provider:physical_network | physnet1                             |
| provider:segmentation_id  | 2198                                 |
| qos_policy_id             |                                      |
| router:external           | False                                |
| shared                    | False                                |
| status                    | ACTIVE                               |
| subnets                   |                                      |
| tags                      |                                      |
| tenant_id                 | 8ed209105d2547fba0e74b9dfbcaa859     |
| updated_at                | 2018-07-24T09:48:10                  |
+---------------------------+--------------------------------------+
- 根据3.b.i查询结果，获取需要放通的vlan号。
- 对于“provider:network_type”为“vlan”，“provider:segmentation_id”即为vlan号。
本例vlan号为2198。
- 对于“provider:network_type”为“vxlan”且OpenStack对接SDN，交换机上的vlan由SDN放通，执行9。
- 对于“provider:network_type”为“vxlan”且OpenStack未对接SDN，执行如下命令，获取vlan号。
cps network-list | grep tunnel_bearing
本例vlan号为4003，如下所示。
B4B7CB29-D21D-B211-8F3E-0018E1C5D866:~ # cps network-list | grep tunnel_bearing
| tunnel_bearing | 1 | internal_auto | 172.28.48.0-172.28.63.255 | 172.28.48.0/20 | | 4003 | | | tx_limit: | | System vm tunnel network. |
- 根据3.b.i查询结果，查询网络对应的nic口。
登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
- 对于“provider:network_type”为“vlan”，选择“配置 > 网络 > 网口映射配置”，找到异常服务所在主机的主机组，查看provider:physical_network对应的nic口。
如下图physnet1对应的nic口为nic0和nic1。
- 对于“provider:network_type”为“vxlan”，选择“配置 > OpenStack > Neutron”，单击“配置Tunnel Bearing网络平面”，找到异常服务所在主机的主机组，查看tunnel_bearing对应的nic口。
如下图tunnel_bearing对应nic0和nic1。
- 执行如下命令，查询nic口对应的eth口。
cat /usr/bin/ports_info | python -mjson.tool
B4B7CB29-D21D-B211-8F3E-0018E1C5D866:~ # cat /usr/bin/ports_info | python -mjson.tool
{
"Logic-phyMapInfo": {
"nic0": "eth0",
"nic1": "eth1",
"nic2": "eth2",
"nic3": "eth3",
"nic4": "eth4",
"nic5": "eth5"
},
……
}
- 查询本机eth口在交换机对应的网口上是否放通该vlan，如果没有放通，则需要放通该vlan。
- 查询网络绑定的port是否有残留。
- 执行命令neutron port-list --network-id network_id --device_owner network:dhcp，获取port ID。
- 执行命令neutron port-show port ID，查看该port属性。
- 1DAB63EF-98F8-6FA4-E811-E0270C10E39A:/home/<USER>
- +-----------------------+--------------------------------------------------------------------------------------+
- | Field                 | Value                                                                                |
- +-----------------------+--------------------------------------------------------------------------------------+
- | admin_state_up        | True                                                                                 |
- | allowed_address_pairs |                                                                                      |
- | binding:host_id       |                                                                                      |
- | binding:profile       | {}                                                                                   |
- | binding:vif_details   | {}                                                                                   |
- | binding:vif_type      | unbound                                                                              |
- | binding:vnic_type     | normal                                                                               |
- | created_at            | 2019-07-30T02:15:03                                                                  |
- | description           |                                                                                      |
- | device_id             | reserved_dhcp_port                                                                   |
- | device_owner          | network:dhcp                                                                         |
- | extra_dhcp_opts       |                                                                                      |
- | fixed_ips             | {"subnet_id": "1c4fa5e5-d4d8-4671-bdd8-fc834282565f", "ip_address": "*************"} |
- | id                    | 7d743220-0e3f-41e6-b7be-c93ff9445306                                                 |
- | mac_address           | fa:16:3e:b0:c3:b4                                                                    |
- | name                  | reserved_distributed_dhcp_port                                                       |
- | network_id            | e12cf0db-d28f-4e7c-ae0f-96516310869d                                                 |
- | port_security_enabled | False                                                                                |
- | project_id            | 8341fdc718b34ee699a7f25461983fda                                                     |
- | qos_policy_id         |                                                                                      |
- | security_groups       |                                                                                      |
- | status                | ACTIVE                                                                               |
- | tags                  | distributed                                                                          |
- | tenant_id             | 8341fdc718b34ee699a7f25461983fda                                                     |
- | updated_at            | 2019-07-30T02:17:33                                                                  |
- +-----------------------+--------------------------------------------------------------------------------------+
当查询结果device_id的值为reserved_dhcp_port，表示该port为残留port，使用neutron port-delete port-id 命令删除该port。
4. 执行以下命令，根据2中获取的“error_info”中的network_id，遍历检查dnsmasq进程是否存在。
ps aux | grep -w dnsmasq | grep -v grep | grep -w network_id
- 若查询结果如下所示，进程状态显示为“T”，表示STOP，执行kill -18 进程号。
本例中进程号为7605。完成后，执行7。
B4B7CB29-D21D-B211-8F3E-0018E1C5D866:~ # ps aux | grep -w dnsmasq | grep -v grep | grep -w 82262c70-e8bf-491c-b289-730f60462741
opensta+  7605  0.0  0.0  13964   940 ?        T    Jul24   0:00 dnsmasq --no-hosts --no-resolv --strict-order --except-interface=lo --pid-file=/var/lib/neutron/dhcp/82262c70-e8bf-491c-b289-730f60462741/pid --dhcp-hostsfile=/var/lib/neutron/dhcp/82262c70-e8bf-491c-b289-730f60462741/host --addn-hosts=/var/lib/neutron/dhcp/82262c70-e8bf-491c-b289-730f60462741/addn_hosts --dhcp-optsfile=/var/lib/neutron/dhcp/82262c70-e8bf-491c-b289-730f60462741/opts --dhcp-leasefile=/var/lib/neutron/dhcp/82262c70-e8bf-491c-b289-730f60462741/leases --dhcp-match=set:ipxe,175 --bind-interfaces --interface=tapc5f8ef1a-54 --dhcp-range=set:tag0,********,static,2592000s --dhcp-option-force=option:mtu,1500 --dhcp-lease-max=256 --conf-file=/etc/neutron/dnsmasq.conf --domain=openstacklocal
- 否则，执行9。
5. 参考neutron-dhcp-agent组件故障处理章节，处理DHCP agent故障。完成后，执行7。
6. 执行以下命令，根据2中获取的“error_info”中的network_id，查询名空间里DOWN的端口。
ip netns exec qdhcp-network_id ifconfig -a
若查询结果如下所示，表示tapc0fa43d7-98状态为DOWN（如果端口是UP，flags里有“UP”标记）。
70033389-3304-74BA-E811-93EBFEF7D209:~ # ip netns exec qdhcp-38f49e94-96f8-4faf-a728-580ce221fe80 ifconfig -a
lo: flags=73<UP,LOOPBACK,RUNNING>  mtu 65536
inet ***********  netmask *********
inet6 FC00::1  prefixlen 128  scopeid 0x10<host>
loop  txqueuelen 1  (Local Loopback)
RX packets 2  bytes 688 (688.0 B)
RX errors 0  dropped 0  overruns 0  frame 0
TX packets 2  bytes 688 (688.0 B)
TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
tapc0fa43d7-98: flags=2<BROADCAST>  mtu 1500
inet *************  netmask *************  broadcast *************
ether fa:16:3e:50:32:7c  txqueuelen 1000  (Ethernet)
RX packets 11055  bytes 765844 (747.8 KiB)
RX errors 0  dropped 4  overruns 0  frame 0
TX packets 10874  bytes 830188 (810.7 KiB)
TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
执行以下命令将名空间里查询到的DOWN的端口设置为UP。
ip netns exec qdhcp-network_id ifconfig 端口 up
结果如下所示：
70033389-3304-74BA-E811-93EBFEF7D209:~ # ip netns exec qdhcp-38f49e94-96f8-4faf-a728-580ce221fe80 ifconfig tapc0fa43d7-98 up
7. 等待5分钟~6分钟，告警是否自动清除。
- 是，处理完毕。
- 否，执行8。
8. 告警error_info是否有更新。
- 是，执行2。
- 否，执行9。
9. 请联系技术支持工程师协助解决。
##### 参考信息
无。