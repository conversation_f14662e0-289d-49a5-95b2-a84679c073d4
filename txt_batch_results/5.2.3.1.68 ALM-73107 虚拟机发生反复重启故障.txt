# 5.2.3.1.68 ALM-73107 虚拟机发生反复重启故障

##### 告警解释
当系统检测到单个虚拟机在10分钟内重启次数不小于5次时，系统产生此告警。
当单个虚拟机在5分钟内重启次数不大于1次时，清除告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73107 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID。 |
| 附加信息 | 异常信息：产生告警的异常信息。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名称。<br>虚拟机名：产生告警的虚拟机名称。<br>主机IP：产生告警的主机IP。 |
##### 对系统的影响
影响该虚拟机上运行的业务。
##### 可能原因
- 告警虚拟机所在的计算节点与存储平面网络链接断开。
- 虚拟机镜像损坏。
- 用户行为，频繁重启虚拟机。
##### 处理步骤
1. 从告警的附加信息中，获取产生告警的主机IP、虚拟机名或虚拟机ID。
2. 通过ping命令检查告警服务器是否与存储面管理ip网络链接断开。
- 是，执行3。
- 否，执行5。
3. 参考ALM-6023 主机存储链路中断，修复计算节点与存储平面的网络链接。
4. 等待5分钟，告警是否清除。
- 是，任务结束。
- 否，执行5。
5. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
6. 选择“资源 > 计算资源 > 虚拟机”。
7. 单击“VNC登录”，通过vnc登录虚拟机，观察虚拟机是否蓝屏或者黑屏，出现蓝屏或者黑屏说明该虚拟机镜像已损坏。
- 是，执行16。
- 否，执行8。
8. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
9. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
10. 执行以下命令，防止系统超时退出。
TMOUT=0
11. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
12. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
13. 执行nova instance-action-list命令，获取虚拟机操作记录，查看是否是用户频繁重启虚拟机。
- 是，执行14。
- 否，服务器异常，执行16。
14. 等待用户不再频繁重启虚拟机，执行15。
15. 等待5分钟，确认告警是否自动消除。
- 是，任务结束。
- 否，执行16。
16. 请联系技术支持工程师协助解决。
##### 参考信息
无。