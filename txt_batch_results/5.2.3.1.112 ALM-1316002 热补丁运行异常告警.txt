# 5.2.3.1.112 ALM-1316002 热补丁运行异常告警

##### 告警解释
在热补丁安装过程中， CPU压力过大，或者安装热补丁后服务器重启异常等，导致热补丁未生效，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1316002 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生该告警的主机ID。 |
| 附加信息 | 服务名：产生该告警的服务名。<br>微服务名：产生该告警的微服务名。<br>主机名：产生该告警的主机名。<br>错误信息：该告警相关的错误信息。 |
##### 对系统的影响
热补丁未生效不影响正在运行的业务，但由于热补丁通常用于解决安全漏洞，未生效说明对应的安全漏洞没有被修复。
##### 可能原因
安装热补丁过程中，主机节点CPU压力大。
##### 处理步骤
查询告警的错误信息，如果是“配置文件不一致”，即“the patch configuration is inconsistent”，执行12。
如果是热补丁异常，即“hotpatch status is abnormal”，具体处理步骤如下：
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
6. 确认ALM-6018 主机CPU占用率超过阈值和ALM-6033 I层服务CPU占用率超过阈值告警是否存在，如果存在说明当前节点CPU压力过大，请先按照相应的告警处理流程清除告警后再执行7。
7. 进入"/opt/hotpatch/UVP"补丁管理目录。
8. 执行如下命令，进入对应异常补丁的目录。
cd 异常补丁目录
每个热补丁有自己的单独目录，例如，告警提示“hostos_2018101107098”热补丁异常，则进入“/opt/hotpatch/UVP/hostos_2018101107098”目录。
9. 执行如下命令，安装热补丁。
sh hotpatch.sh install
10. 执行如下命令，查询热补丁状态。
sh hotpatch.sh query
执行命令，查看查询命令执行的返回值。
echo $?
- 返回值0，执行11。
- 返回值非0，执行12。
11. 热补丁状态正常，请确认下一个检查周期后（12小时），告警是否自动清除。
- 是，处理完毕。
- 否，执行12。
12. 请联系技术支持工程师协助解决。