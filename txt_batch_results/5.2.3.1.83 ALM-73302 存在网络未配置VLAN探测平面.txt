# 5.2.3.1.83 ALM-73302 存在网络未配置VLAN探测平面

##### 告警解释
目前版本通过VLAN探测平面保证HA虚拟机可靠性，在不配置时虚拟机触发HA可能存在脑裂风险。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73302 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 物理网络平面：产生告警的物理网络平面<br>云服务：产生告警的云服务<br>服务：产生告警的服务 |
| 附加信息 | 异常信息：告警的异常信息 |
##### 对系统的影响
使用该网络的虚拟机在HA时存在脑裂风险。
##### 可能原因
- 虚拟机使用了未配置HA探测VLAN的物理网络，在HA过程中存在脑裂风险。
- 物理网络平面配置探测VLAN后，在主机上创建对应端口时出现异常。
##### 处理步骤
1. 按照告警的详细信息，获取“物理网络平面”参数值。
2. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
3. 依次进入“配置 > 网络 > 物理网络配置”，查看告警信息中的物理网络是否配置了探测VLAN。
- 是，执行7。
- 否，执行4。
4. 现网网络规划该网络是否需要配置探测VLAN。
- 是，执行5。
- 否，单击对应行修改图标弹出修改物理网络窗口，勾选“探测VLAN未配置不产生告警”后提交。然后执行6。
5. 配置探测VLAN。
配置探测VLAN的指导请参考：
请参考手动调整物理网络平面映射关系章节中“配置管理与业务网络平面”部分，配置探测VLAN。
配置是否成功。
- 执行成功，执行6。
- 执行失败，执行7。
6. 在Service OM界面，单击“服务列表 > 集中运维 > 告警 > 告警列表”，单击“清除”，完成操作。
7. 请联系技术支持工程师协助解决。
##### 参考信息
无。