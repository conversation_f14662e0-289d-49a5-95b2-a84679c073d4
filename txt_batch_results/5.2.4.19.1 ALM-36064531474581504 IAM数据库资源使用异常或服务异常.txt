# 5.2.4.19.1 ALM-36064531474581504 IAM数据库资源使用异常或服务异常

##### 告警解释
IAM在定时任务中检测到Redis连接数超过阀值、获取Token失败、IAM服务握手失败、数据库Session连接数超过阈值，基于事件聚合规则（30分钟内大于2条IAM数据库资源使用异常或服务异常事件）产生此告警。产生的告警需在对应事件的附加信息中查看具体原因。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 36064531474581504 | 重要 | 业务质量告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测到异常IAM服务所在服务器节点IP。 |
| 定位信息 | 包含规则ID和事件名称。 |
##### 对系统的影响
ManageOne中IAMCacheProxyService服务或IAMCoreService服务运行异常，可能导致依赖IAM服务的业务无法正常使用IAM的对外接口功能。
##### 可能原因
- IAMCacheProxyService服务异常无法正常生成Token。
- IAMCacheProxyService服务操作Redis失败或者Redis连接数超过阈值。
- IAMCacheProxyService服务操作数据库失败或者数据库Session连接数超过阈值。
- IAMCacheProxyService与IAMCoreService握手失败。
##### 处理步骤
1. 使用PuTTY，登录告警发生所在节点。在该告警的“定位信息”列获取告警发生的节点IP。
默认账号：sopuser，默认密码：*****。
2. 执行如下命令，切换root用户。
sudo su root
默认密码：*****。
3. 执行如下命令，收集IAMCacheProxyService服务运行日志信息。
tar -zcvf /tmp/IAMCacheProxyService-节点IP地址-log.tar.gz /var/log/oss/Product/IAMCacheProxyService/iamcacheproxy*/log/root.log
4. 执行如下命令，收集IAMCoreService服务运行日志信息。
tar -zcvf /tmp/IAMCoreService-节点IP地址-log.tar.gz /var/log/oss/Product/IAMCoreService/root.log
5. 根据3和4收集的日志信息，联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警。
##### 参考信息
无。