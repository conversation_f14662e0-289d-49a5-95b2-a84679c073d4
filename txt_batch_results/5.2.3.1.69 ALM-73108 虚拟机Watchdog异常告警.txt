# *******.69 ALM-73108 虚拟机Watchdog异常告警

##### 告警解释
当虚拟机Watchdog对应的IPMI模拟进程退出或进程状态异常时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73108 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：异常虚拟机的UUID。 |
| 附加信息 | 异常信息：表示虚拟机异常的具体信息，包括虚拟机名称和uuid。<br>主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>虚拟机名：异常虚拟机在UVP层的名称。<br>主机IP：产生告警的主机IP。 |
##### 对系统的影响
虚拟机内部卡死之后无法正常上报Watchdog超时事件。
##### 可能原因
- 虚拟机开启Watchdog功能之后，对应的IPMI模拟进程退出。
- 虚拟机开启Watchdog功能之后，对应的IPMI模拟进程为T或者Z状态。
##### 处理步骤
1. 获取告警附加信息中的主机IP，使用PuTTY，通过主机IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行如下命令，重启vBMC_agentd服务，等待10秒钟确认告警是否自动消除。
systemctl restart vBMC_agentd
- 是，执行完毕。
- 否，执行4。
4. 执行如下命令，重启sysmonitor服务，等待10秒钟确认告警是否自动消除。
systemctl restart sysmonitor
- 是，执行完毕。
- 否，执行5。
5. 执行如下命令，查看告警对应的虚拟机状态是否为Active。
nova_virsh_cmd virsh-instance-state VMName
其中VMName为告警对象ID，比如nova_virsh_cmd virsh-instance-state instance-00000050。
- 是，执行6。
- 否，请手动清除告警。
6. 执行如下命令，关闭虚拟机。
nova_virsh_cmd virsh-instance-shutdown VMName
等命令返回success时，说明虚拟机关闭。
7. 执行如下命令，启动虚拟机。
virsh start VMName
等命令返回类似Domain instance-00000126 started信息时，说明虚拟机启动。
8. 待虚拟机启动后等待10秒钟，确认告警是否自动消除。
- 是，处理完毕。
- 否，执行9。
9. 请联系技术支持工程师协助解决。
##### 参考信息
无。