# ********.7 0x3230030 HA心跳中断

##### 告警解释
主节点未接收到备节点的心跳信息。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x3230030 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主节点名称 | 主节点的名称 |
| 主节点IP地址 | 主节点的IP地址 |
| 备节点名称 | 备节点的名称 |
| 备节点IP地址 | 备节点的IP地址 |
##### 对系统的影响
可能引起仲裁，使备端切换为主端。
##### 可能原因
- 主节点或备节点处于异常状态（如复位、下电等）。
- 网络连接异常，造成链路中断。
- 网络配置变更，造成链路中断。
##### 处理步骤
1. 使用PuTTY，以告警详细信息当中的主节点IP地址登录主节点服务器后台操作系统。
默认帐号：DRManager，默认密码：*****。
2. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
3. 检查主节点是否可以ping通备节点。如果是IPv4，执行ping 备节点IP地址命令，如果是IPv6，执行ping6 备节点IP地址命令，检查是否可以ping通。备节点IP地址通过告警详细信息当中的备节点IP地址获取。
- 是，转到4。
- 否，请修复主节点和备节点间的网络连接，修复成功后，等待5分钟，检查告警是否自动清除，如果已清除，流程结束，否则请转4。
4. 使用PuTTY，以主节点和备节点字段对应的IP地址登录主节点和备节点。
默认帐号：DRManager，默认密码：*****。
5. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
6. 执行cd /opt/BCManager/Runtime/bin;sh showSystemStatus.sh查看主节点和备节点上的服务状态。
如果命令回显中ResStatus列中的值存在非Normal或Active_normal，表示有服务处理未运行状态。请转7。
如果命令回显中ResStatus的值为Normal或Active_normal，表示服务都处于运行状态，请联系技术工程师协助解决。
7. 等待10分钟后，检查服务是否都已经处于运行状态。
- 是，检查告警是否自动清除，如果已清除，流程结束。如果未清除，请联系技术工程师协助解决。
- 否，请联系技术工程师协助解决。
##### 参考信息
无。