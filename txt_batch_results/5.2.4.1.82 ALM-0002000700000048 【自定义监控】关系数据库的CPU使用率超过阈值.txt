# 5.2.4.1.82 ALM-0002000700000048 【自定义监控】关系数据库的CPU使用率超过阈值

##### 告警解释
当性能监控检测到CPU使用率超过管理员设置的阈值时，产生此通知信息。
阈值需要在“监控中心 > 监控配置 > 性能阈值维护”中手工设置。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0002000700000048 | 紧急/重要/次要/提示 | 业务质量告警 |
性能监控阈值通知共有四个级别，分别是：紧急、重要、次要和提示。四个级别的通知处理方式一致。
##### 告警参数
| 资源类型 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 关系数据库 | 名称 | 关系数据库的资源名称。 |
| 关系数据库 | IP地址 | 关系数据库的IP地址。 |
##### 对系统的影响
可能导致系统功能不可用。
##### 可能原因
阈值条件设置不合理。
##### 处理步骤
1. 检查监控指标的阈值设置。
- 在主菜单中选择“监控中心 > 监控配置 > 性能阈值维护”。
- 在“性能阈值维护”页面，找到对应监控指标的阈值。
- 单击对应监控指标的，查看阈值条件设置是否合理（阈值设置的参考值分别为：紧急 95%，重要 90%，次要 85%，提示 80%）。
- 是：执行3。
- 否：执行2。
2. 修改指标阈值策略。
- 根据实际指标数据修改阈值。
- 等待5分钟，在主菜单选择“集中告警 > 当前告警”，检查通知信息是否清除。
- 是：处理完毕。
- 否：执行3。
3. 业务正常情况下，联系业务运维人员进行扩容、迁移业务，其他情况请联系技术工程师协助解决。
##### 告警清除
修复后，系统会自动清除此通知信息，无需手工清除。
##### 参考信息
无