# 5.2.4.16.1 ALM-10000011 ManageOne管理资源超过部署规格限制

##### 告警解释
MOAutoOps定期执行脚本，计算出等效虚拟网元数量，以此等效虚拟网元数量和Region数量来匹配ManageOne的部署规格，如果匹配出的规格超过ManageOne部署规格限制时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 10000011 | 紧急 | 环境告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 当前等效网元数 | 当前环境管理设备的等效网元数。 |
| 最大支持网元数 | 最大支持的等效网元数。 |
| 当前Region总数 | 当前环境的Region总数。 |
| 最大支持Region总数 | 最大支持的Region总数。 |
| 当前私有云Region数 | 当前环境完整的私有云Region数量，不包括通过HiCloud、IAAS-E、IAAS-V接入的Region。 |
| 最大支持私有云Region数 | 最大支持的私有云Region数。 |
##### 对系统的影响
可能导致任务失败、接口超时、页面反应变慢，甚至内存溢出、节点重启。
##### 可能原因
ManageOne管理设备数量或Region数量超过ManageOne部署规格限制。
##### 处理步骤
1. 查看当前告警定位信息。
- 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
- 在主菜单中选择“集中告警 > 当前告警”。
- 单击“ManageOne管理资源超过部署规格限制”告警左侧的，在“告警详情”页签的“定位信息”中，查看当前管理资源数目和ManageOne部署规格限制。
包括：当前等效网元数、最大支持网元数、当前Region总数、最大支持Region总数、当前私有云Region数和最大支持私有云Region数。
- 如果只有当前等效网元数大于最大支持网元数，执行2。
- 如果存在当前Region总数或当前私有云Region数大于对应最大支持规格的情况，执行6。
2. 通过“运维自动化”界面的定期执行脚本，下载告警信息收集文件。
- 在主菜单中选择“运维自动化 > 作业 > 作业历史”，进入“作业历史”页面。
- 搜索作业名称为“检查ManageOne管理资源”的作业历史，并在最新执行时间所在行的“操作列”单击“下载结果”。
- 从所下载的Excel中，可查看到当前ManageOne所管理资源的资源名称、资源数量、单位等效网元和等效网元数。
3. 判断是否可通过调整资源数量，使Excel中的等效网元数总和小于最大支持网元数。
- 根据现网环境评估哪些云服务和基础设施资源可释放。
- 根据评估结果，在Excel表格“资源数量”列，调整对应的云服务或基础设施的数量。
- 查看调整后的等效网元数总和，确认等效网元数总和是否小于最大支持网元数。
- 是，执行4。
- 否，执行6。
4. 释放符合要求的闲置资源。
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 扩容解决方案，使ManageOne部署规格限制满足管理资源要求。
请参考《华为云Stack 6.5.1 扩容指南》中的“扩容管理规模”章节。
7. 查看告警是否清除。
- 是，处理完毕。
- 否，联系技术工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。