# 5.2.6.4.3 登录FusionSphere OpenStack后台

在系统经过SSH安全加固后，禁用了SSH通过口令认证的访问方式。本操作指导用户使用PuTTY工具和认证所需的私钥，来登录待访问的节点。
##### 前提条件
- 待访问节点已完成SSH的安全加固，包括：
- 启用SSH通过公私钥对认证的方式，且节点已经配置好公钥证书。
- 已禁用SSH通过口令认证的方式。
- 已获取与公钥证书匹配的私钥证书。如果私钥证书为加密证书，还需要获取私钥证书的密码。
默认的私钥证书获取方式如下：
访问http://support.huawei.com/并登录，网页右上角搜索“FusionSphere默认公私钥”，通过搜索结果中的第一个条目（FusionSphere 默认公私钥【文档】）获取证书。
##### 操作步骤
1. 在当前的本地PC上，是否已经使用PuTTY工具，通过公私钥对认证方式登录过待访问的节点？
- 是，执行7。
- 否或无法确定，执行2。
2. 运行PuTTY工具，在主界面，输入待访问节点的IP地址和SSH端口号（默认为22）。
3. 在左侧“Category”区域选择“Connection > SSH > Auth”。
进入SSH的认证配置界面。
4. 单击“Browse”，在弹出窗口中选择已获取的私钥证书，单击“打开”。
私钥证书文件名为“*.ppk”。当前默认的私钥证书为从版本发布路径获取的“id_rsa.ppk”。
配置后如下图所示。
图1 配置私钥证书
5. 在左侧“Category”区域选择“Session”。
进入主界面。
6. 为方便后续多次访问，在“Saved Sessions”中自定义会话名称，单击“Save”保存会话。
如下图所示。
图2 保存会话
该步骤执行完成后，跳转至8。
- Host Name (or IP address)输入框ip（例如***************）的获取方式：在“FCD生成的LLD”文件中，搜索Reverse-Proxy，找到网络平面为external_api对应的IP地址。该IP地址即为FusionSphere OpenStack的后台节点。
7. 选择已保存的会话，单击“Load”加载会话。
8. 单击“Open”开启会话。
9. 按要求输入访问节点的登录用户名，即可开始访问节点。
如果使用的私钥证书为加密证书，则还要按提示输入私钥证书的密码。
版本配套的默认私钥证书为加密证书，默认密码为“*****”。
- FusionSphere OpenStack后台节点默认用户为fsp，默认密码为“*****”。
- 登录fsp用户后，输入命令su，切换到root用户，默认密码为“*****”。
- 切换到root用户后，一般需要参考导入环境变量，导入环境变量。