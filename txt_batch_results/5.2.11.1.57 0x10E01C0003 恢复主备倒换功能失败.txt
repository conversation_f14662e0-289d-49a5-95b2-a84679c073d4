# 5.2.11.1.57 0x10E01C0003 恢复主备倒换功能失败

##### 告警解释
取消禁止主备倒换失败，HA功能异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0003 | 紧急 | 是 |
##### 对系统的影响
HA无法进行主备倒换。
##### 可能原因
- HA主节点和备节点之间的网络连接中断。
- 网络性能差。
- 内部处理错误。
##### 处理步骤
- 可能原因1：HA主节点和备节点之间的网络连接中断。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看HA的主节点和备节点的IP地址。
- 使用PuTTY，登录HA的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping HA的备节点IP地址”，如果是IPv6，执行“ping6 HA的备节点IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络连通性，确保网络正常连接。
- 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证HA主节点和备节点之间通信稳定。
- 否，执行3。
- 可能原因3：内部处理错误。
- 使用PuTTY，登录HA的主节点。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 执行“cd /opt/huawei-data-protection/ebackup/ha/module/hacom/tools”命令，进入tools目录。
- 执行“./ha_client_tool --cancelforbidswitch”命令，查看命令是否可以执行成功。
- 是，执行3.e。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
##### 参考信息
无