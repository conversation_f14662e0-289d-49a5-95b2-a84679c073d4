# ********.26 0x210000000200 证书校验失败

##### 告警解释
与LDAP服务器（IP：[IP_Address]）之间的连接没有可匹配的CA证书。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000200 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_Address | LDAP服务器的IP地址。 |
##### 对系统的影响
与LDAP服务器之间的连接存在安全风险。
##### 可能原因
系统中不存在连接该LDAP服务器的CA证书或CA证书已过期。
##### 处理步骤
- 可能原因1：系统中不存在连接该LDAP服务器的CA证书或CA证书已过期。
- 请联系管理员获取未过期的LDAP服务器的CA证书文件。
- 将获取到的LDAP服务器的CA证书文件重命名为“LDAP_CACert.crt”。
- 通过WinSCP工具将“LDAP_CACert.crt”文件拷贝到Manager或Server的“/home/<USER>
登录方式请参考登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行mv /home/<USER>/LDAP_CACert.crt /opt/huawei-data-protection/ebackup/microservice/ebk_iam/conf命令，将证书文件移动到IAM微服务的配置目录中。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_iam/conf命令，进入证书保存目录。
- 执行chmod 600 LDAP_CACert.crt命令，将“LDAP_CACert.crt”文件的权限设置为600。
- 执行chown hcpprocess:hcpmgr LDAP_CACert.crt命令，将“LDAP_CACert.crt”文件的所有者修改为hcpprocess:hcpmgr。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_iam/script命令，进入微服务脚本目录。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止IAM微服务。等待1分钟左右，IAM微服务会自动启动。
- 以LDAP类型的帐户登录Manager或Server的GUI，以此触发证书校验。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无