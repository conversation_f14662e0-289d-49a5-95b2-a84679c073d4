# 5.2.4.13.1 ALM-0001000300030001 Elasticsearch集群心跳检测异常

##### 告警解释
Elasticsearch集群心跳异常，当连续五次集群节点之间心跳检测异常，并且节点故障个数超过50%则上报告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 0001000300030001 | 紧急 | 环境告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 云服务 | ManageOne |
| 服务 | MOLog |
| 组件 | Elasticsearch集群 |
##### 对系统的影响
- 依赖于调用链的故障定位定界不可用。
- 管理操作日志页面无数据。
- 运行日志页签下的集群状态页面无数据。
##### 可能原因
- 网络异常。
- ElasticSearch集群节点最近十分钟心跳检测异常。
##### 处理步骤
1. 登录部署面查看服务所在的节点。
- 登录ManageOne安装部署界面。
登录地址：https://ManageOne部署面地址。
默认帐号：admin，默认密码：*****。
- 在主菜单中选择“应用管理 > 管理产品软件”。
- 单击“ManageOne”产品对应的卡片。
- 在“部署历史”中最新部署的那一行对应的“操作”列单击，进入服务部署列表。
在搜索框中输入Molog所属的服务名称“mo_om_adminplane”，单击“Enter”，查询该服务的部署列表。
如果未查询到该服务对应的部署列表，则返回“部署历史”，在“部署历史”中下一行对应的“操作”列单击，继续查询，以此类推。
- 单击搜索到的具体的实例名称，进入服务部署详情页面。
- 在“部署历史”中最新部署的那一行对应的“部署状态”列单击“成功”。
- 在“资源部署状态”中找到“名称”包含MOLogStorageService微服务且“类型”为“Stage”的资源名称，并在对应的“操作”列单击。
- 在“节点IP”中查看并获取MOLogStorageService所在的节点IP地址。
2. 查看ManageOne运维面该告警对应的异常节点IP地址。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在主菜单中选择“集中告警 > 当前告警”。
- 单击操作列左侧的，进入“告警详情”页签。
- 查看“附加信息”中的异常节点IP地址。
3. 登录MOLogStorageService所在的节点。
- 对比2与1中的IP地址是否一致。
- 是，则登录任意节点。
- 否，则登录正常节点。
- 使用PuTTY，根据3.a中的判断结果，通过对应的节点IP地址登录节点。
默认帐号：sopuser，默认密码为*****。
4. 执行如下命令，防止系统超时退出。
TMOUT=0
5. 执行如下命令，切换至root用户。
sudo su root
用户root的默认密码为*****。
6. 执行如下命令，切换ossadm用户。
su ossadm
7. 执行如下命令，检查虚拟机之间是否可以通信。
ping MOLogStorageService异常节点IP地址
如果有类似以下回显，则证明虚拟机节点通信正常。
64 bytes from 节点IP: icmp_seq=1 ttl=64 time=0.019 ms
64 bytes from 节点IP: icmp_seq=2 ttl=64 time=0.036 ms
是，执行8。
否，执行9。
8. 登录异常节点，执行以下命令，重启服务。
/opt/oss/manager/agent/bin/ipmc_adm -cmd restartapp -app MOLogStorageService
重启后如果有类似以下回显，则证明重启成功。
Stopping process mologstorageservice-10-0 ... success
Starting process mologstorageservice-10-0 ... success
所有节点重启完成后，约20分钟之后查看告警是否消除，如还未消除，执行9。
9. 请联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无