# 5.2.3.1.31 ALM-70104 主机内存使用超过主机总内存

##### 告警解释
主机上内存使用超过主机总内存时产生告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70104 | 提示 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 服务名：产生告警的服务名 |
| 附加信息 | 集群总内存大小：告警主机所在集群总内存大小<br>集群已经分配的内存大小：告警主机所在集群已经分配的内存大小<br>主机名：告警主机名称 |
##### 对系统的影响
FusionSphere OpenStack 对接FusionCompute的场景下，该主机上的虚拟机进行大量内存使用业务时，虚拟机性能有所下降，或者主机上已经关闭的虚拟机上电失败，影响客户体验。
##### 可能原因
- 主机内存资源将耗尽时，同时在该主机上并发创建了多个虚拟机。
- 对接FusionCompute集群开启了HA资源预留。
##### 处理步骤
1. 通过告警定位信息找到产生告警的fc-nova-compute节点所对应的FusionCompute集群。
2. 将该fc-nova-compute节点所对应的集群中部分虚拟机迁移到其他fc-nova-compute节点上。
具体操作请参考迁移虚拟机。
3. 如果上述操作出现问题，请联系技术支持工程师协助解决。
##### 参考信息
无。