# 5.2.11.1.17 0x1000310000 事件转储目录所用空间已超出阈值

##### 告警解释
事件转储目录（[Dump_dir]）所用空间已超过预设的阈值（[Threshold_size]MB），当目录大小超过最大值（[Max_size]MB）后，程序将自动删除最旧的转储文件。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000310000 | 紧急 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Threshold_size | 允许转储目录使用空间的阈值。 |
| Max_size | 允许转储目录使用的最大空间。 |
| Dump_dir | 事件转储路径。 |
##### 对系统的影响
系统事件记录可能丢失。
##### 可能原因
事件转储目录下所保存的文件太多。
##### 处理步骤
- 可能原因1：事件转储目录下所保存的文件太多。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“cat /opt/huawei-data-protection/ebackup/microservice/ebk_alarm/conf/hcpconf.ini | grep 'EventDumpDirPath'”命令，获取事件转储目录。
- 执行cd EventDumpDirPath命令进入事件转储目录，其中EventDumpDirPath为1.c获取的事件转储目录。
- 请备份事件转储文件。
- 手动删除已经备份的事件转储文件，使“EventDumpDirPath”目录的使用空间小于阈值。
##### 参考信息
无