# 5.2.4.18.2.1 ALM-151 CPU占用率过高告警

##### 告警解释
部署面会对服务器CPU的使用率进行持续采样。在连续的采样周期（过载次数×15秒）内，每次采样的CPU（Central Processing Unit）使用率均大于等于告警产生阈值时，产生该告警。如果采样周期内有一次CPU使用率采样小于告警产生阈值时，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 151 | 重要 | 越限 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 主机 | 产生告警的节点名称。 |
| 操作系统 | 服务器的操作系统。 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
- 部署面响应变慢，操作出现延时。
- 实时上报性能、告警等出现延迟，不能及时获取信息。
- 业务处理缓慢，可能导致消息堆积。
##### 可能原因
- 部署面临时性的系统繁忙。
- 部署面 CPU使用率的告警产生阈值设置不合理。
- 部署面正在执行消耗系统资源的操作。
- 部署面硬件性能较低，不支持部署面的正常运行。
##### 处理步骤
1. 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
2. 检查部署面是否存在多个任务正在执行。
在部署面主菜单中选择“系统 > 任务列表”，等待当前所有任务结束后，查看告警是否清除。
- 是，告警处理结束。
- 否，请执行3。
3. 检查CPU使用率的阈值设置是否合理。
- 在部署面主菜单中选择“产品 > 系统监控”。
- 在“系统监控”界面的“节点”页签右侧单击，检查CPU使用率的“产生告警阈值”和“过载次数”是否设置合理。
- 是，执行4。
- 否，重新设置“产生告警阈值”和“过载次数”为合理的值（缺省值分别为85和40）。告警处理结束。
4. 检查是否存在应用程序CPU占用率超过告警产生阈值。
- 在“节点”页签下，查找产生告警的节点名称。
- 查看对应的CPU使用率是否超过设定的告警产生阈值。
- 是，可能由于应用程序导致CPU资源耗尽，待进程对应的业务处理完毕后，执行7。若无法等待业务处理完毕，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
- 否，执行5。
5. 检查是否存在非应用程序CPU占用率超过告警产生阈值。
- 使用PuTTY工具以sopuser用户通过SSH方式登录告警参数中“主机”对应的IP地址，请在如何查找节点对应的IP地址中查询节点对应的IP地址。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 通过执行top命令，在“CPU”列查看对应进程的CPU使用率。
- 是，联系华为技术支持工程师协助解决。告警处理结束。
- 否，执行6。
6. 检查服务器硬件性能是否较低。
服务器硬件性能较低的表现为：
- 系统管理规模对应的硬件要求超过服务器硬件实际能力。
- 经常连续或频繁收到此告警。
检查是否符合以上两个表现：
- 是，联系华为技术支持工程师协助解决。告警处理结束。
- 否，执行7。
7. 等待1分钟，查看本告警是否清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
8. 产生该告警的节点名称发生了变化。
9. 在告警产生后升级了操作系统或者安装了操作系统补丁。
10. 产生该告警的站点名称发生了变化。
11. 产生该告警的服务器不被监控了。
##### 参考信息
无。