# 5.2.11.3.17 0x323003D 受保护虚拟机从服务实例中被移除

##### 告警解释
服务实例中虚拟机被移除，导致该虚拟机不能被保护组保护。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003D | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务实例名称 | 服务实例的名称 |
| 虚拟机名称 | 服务实例中已经被移除的虚拟机名称 |
##### 对系统的影响
虚拟机被删除/迁移，导致该虚拟机不在服务实例中或部分在服务实例中。
##### 可能原因
- 虚拟机被删除。
- 虚拟机没有使用服务实例对应的数据存储。
##### 处理步骤
1. 确认虚拟机是否已经被删除。具体操作请参见筛选与搜索。确认是否存在该虚拟机。
- 否，则手工清除告警。如果告警未清除，请转2。
- 是，请转2。
2. 请联系技术支持工程师协助解决。
##### 参考信息
无。