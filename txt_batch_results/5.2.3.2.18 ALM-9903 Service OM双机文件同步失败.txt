# *******.18 ALM-9903 Service OM双机文件同步失败

##### 告警解释
系统5分钟进行主备节点文件全同步，全同步失败则产生告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9903 | 提示 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 本端地址：ServiceOM主节点IP地址<br>对端地址：ServiceOM备节点IP地址 |
##### 对系统的影响
Service OM主备节点文件系统无法保持一致性，当主备倒换时，备节点升主后可能导致部分文件在原主节点的修改丢失，造成部分业务配置失效。
##### 可能原因
- 主节点文件HA同步服务异常。
- 网络闪断。
- 节点重启。
##### 处理步骤
1. 使用“PuTTY”，以“galaxmanager”用户通过管理浮动IP地址登录Service OM主节点。
默认帐号：galaxmanager，默认密码：*****
登录FusionSphere OpenStack的安装部署界面，具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。选择“云化服务 > FusionSphere OpenStack OM”，查看OM列表即可获取管理浮动IP信息。
2. 执行以下命令，检查HA主备状态是否正常。
QueryHaState om
输出结果类似如下显示，表明当前HA状态正常。
LOCAL_HOST=allinonefm0
LOCAL_STATE=standby
LOCAL_IP=**************
REMOTE_HOST=allinonefm1
REMOTE_STATE=active
REMOTE_IP=**************
如果LOCAL_STATE或REMOTE_STATE存在unknow，则表明当前HA状态存在异常。
- HA状态正常，执行8。
- LOCAL_STATE状态异常，执行3。
- REMOTE_STATE状态异常，执行5。
3. 执行以下命令，重启HA。
haStopAll -r;haStartAll -r
4. 执行8。
5. 使用“PuTTY”，以“galaxmanager”用户登录备节点。
备节点IP可以由2中的REMOTE_IP获取。
6. 执行以下命令，重启HA。
haStopAll -r;haStartAll -r
7. 执行8。
8. 15分钟以后，查看告警是否清除。
- 是，处理完毕。
- 否，执行9。
9. 请联系技术支持工程师协助解决。
##### 参考信息
无。