# 5.2.11.1.79 0x10E01A0010 备份副本中缺失受保护对象元数据

##### 告警解释
在执行校验受保护对象（名称：[Machine_name]，UUID/GUID：[Machine_UUID]，备份副本ID：[Snap_id]，创建时间：[Create_time]）的任务时，检测到受保护对象的元数据缺失（或部分缺失），或元数据损坏。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01A0010 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Snap_id | 备份副本ID。 |
| Create_time | 备份副本创建时间。 |
| Machine_name | 备份对象名称。 |
| Machine_UUID | 备份对象UUID。 |
##### 对系统的影响
基于此备份副本的下一次备份任务将提升为全量备份。
##### 可能原因
- 存储单元不可访问。
- 备份副本的受保护对象元数据缺失。
##### 处理步骤
- 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为 0x10E00E0000（连接存储单元失败） 或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
- 可能原因2：备份副本的受保护对象元数据缺失。
请联系技术支持工程师协助解决。
##### 参考信息
无。