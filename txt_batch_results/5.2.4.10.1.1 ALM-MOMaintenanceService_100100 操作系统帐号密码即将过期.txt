# 5.2.4.10.1.1 ALM-MOMaintenanceService_100100 操作系统帐号密码即将过期

##### 告警解释
系统维护定时检测操作系统帐号过期情况，当到期时间距离当前时间间隔小于等于30天时，产生此告警，告警级别为重要，当到期时间距离当前时间间隔小于等于7天时，告警级别为紧急。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOMaintenanceService_100100 | 紧急/重要 | 环境告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 节点IP地址 | 被检测操作系统帐号所在节点的IP地址。 |
| 帐号名称 | 被检测的操作系统帐号名称。 |
| 帐号密码即将过期天数 | 被检测帐号密码即将过期的天数。 |
##### 对系统的影响
操作系统帐号过期不可用，可能影响业务功能的正常运行。
##### 可能原因
操作系统帐号对应的密码即将过期。
##### 处理步骤
1. 检查操作系统帐号密码到期时间距离当前时间是否小于等于30天或小于等于7天。
- 使用PuTTY，登录告警发生所在节点IP。在该告警的“定位信息”列获取告警发生的节点IP。
默认帐户：sopuser，默认密码：*****。
- 执行如下命令，切换用户到root。
sudo su root
默认密码：*****。
- 切换至root用户，执行chage命令，查看帐户到期时间。例如，查看ossuser帐户的到期时间，执行如下命令。
chage -l ossuser
回显信息如下所示，其中“Password expires”对应的时间为该帐户密码的到期时间。
Last password change                                    : Jun 15, 2018
Password expires                                        : Dec 12, 2018
Password inactive                                       : Jan 16, 2019
Account expires                                         : never
Minimum number of days between password change          : 7
Maximum number of days between password change          : 180
Number of days of warning before password expires       : 15
- 计算帐户到期时间距离当前时间是否小于等于30天或小于等于7天。
- 是，执行2。
- 否，执行4。
2. 修改密码。
- 如果是root 、ossuser、dbuser、ossadm和sopuser帐户，执行passwd命令，修改操作系统即将过期的帐号密码。例如，修改ossuser帐户的密码，执行如下命令。
passwd ossuser
回显如下信息，根据提示输入新密码，并且再次确认新密码。
Changing password for user ossuser.
New password:
Retype new password:
如果用户需要再次修改为默认密码，请执行以下操作。
- 执行如下命令，登录虚拟机。
ssh username@IP
username是待修改密码的帐户。IP是帐户所在节点的IP地址。
- 执行如下命令，切换至root用户。
sudo su - root
默认密码为：*****。
- 执行以下命令，清空旧密码的记录。
true>/etc/security/opasswd
- 执行以下命令，修改为原来的默认密码。
passwd username
- 如果是meteradmin、taskfileadmin、autoopsadmin和logctadm帐户密码过期，由于这些帐户涉及与其他云服务组件交互，如果修改为非默认密码，会影响业务，建议修改密码有效期来避免因密码过期而导致服务不可用。建议将密码修改为永久不过期密码。
以meteradmin帐户为例，执行如下命令。
chage -M 180 meteradmin
其中，180为天数，设置为99999为永久不过期。
3. 待凌晨一点自动执行操作系统帐户密码过期检查之后，查看告警是否清除。
- 是，处理完毕。
- 否，执行4。
4. 请收集上述告警处理过程中的信息，联系技术支持工程师协助解决。
5. 执行如下命令，退出root用户。
exit
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。