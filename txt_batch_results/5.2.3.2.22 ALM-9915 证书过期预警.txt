# 5.2.3.2.22 ALM-9915 证书过期预警

##### 告警解释
Service OM周期（默认为1天）检查环境上的证书是否过期，如果存在证书未到生效时间、即将过期或者已经过期，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9915 | 紧急/重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>证书类型：默认证书的类型 |
| 附加信息 | 开始时间：证书开始时间<br>结束时间：证书结束时间<br>详细信息：证书即将过期或者还未生效或者已经过期的时间说明 |
##### 对系统的影响
如果环境上的证书过期， 且在证书认证开关打开的情况下，则服务和命令行将不可用。
##### 可能原因
- 组件的证书还未到生效时间。告警级别：紧急。
- 组件的证书即将（30天内）过期。告警级别：重要。
- 组件的证书马上（7天内）过期。告警级别：紧急。
- 组件的证书已经过期。告警级别：紧急。
##### 处理步骤
1. 查看告警详细信息，根据证书开始和结束的时间，判断证书状态。
- 证书未到生效时间或证书已经过期，执行2。
- 证书即将过期，参考通过ManageOne界面方式单个或批量更换证书。
2. 参考证书管理 > 附录中的以下章节，分别更新下列证书：
- “手动恢复FusionSphere OpenStack SSL证书”
- “手动恢复FusionSphere-Keystone PKI证书”
- “更换Service OM的HA服务认证证书”
- “更换Service OM的Web服务器nginx证书”
- “更换Service OM的数据库证书”
- “更换Service OM的backup-client证书”
3. 在替换完所有证书之后，等待一小时左右，检查告警是否自动清除。
- 是，处理完毕。
- 否，执行4。
4. 请联系技术支持工程师协助解决。
##### 参考信息
无。