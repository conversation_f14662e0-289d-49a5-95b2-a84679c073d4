# 5.2.3.1.100 ALM-1126002 裸金属服务器审计告警

##### 告警解释
当执行系统审计时发现非正常状态的裸金属服务器实例，产生此告警。当审计结果中不存在非正常的裸金属服务器实例，该告警清除。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1126002 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务名称，默认为OpenStack<br>服务：产生告警的服务名称，默认为ironic |
| 附加信息 | 云服务：产生告警的微服务名称，默认为ironic<br>详细信息：裸金属服务器审计产生告警的详细信息 |
##### 对系统的影响
此告警产生时，系统中存在不正常状态的裸金属服务器，影响系统对裸金属服务器的管理。
##### 可能原因
- 系统存在野物理服务器。
- 系统存在假物理服务器。
- 系统存在不可用物理服务器。
- 系统存在处于中间态的物理服务器。
请在告警的详细信息中获取引发告警的具体审计问题，并参考处理步骤完成相应问题的处理。
##### 处理步骤
1. 获取告警详情中“附加信息”参数中的“详细信息”取值，并参考表1，获取对应的审计报告名称。
| 表1 详细信息与审计报告的对应关系 | 表1 详细信息与审计报告的对应关系 |
| --- | --- |
| 详细信息 | 审计报告 |
| invalid_ironic_instances | invalid_ironic_instances.csv |
| invalid_ironic_nodes | invalid_ironic_nodes.csv |
| stucking_ironic_instances | stucking_ironic_instances.csv |
2. 确定当前环境部署的场景，获取审计报告。
- Region Type I：
KVM虚拟化（被级联层）：收集审计报告
- Region Type II&Region Type III：
KVM虚拟化：收集审计报告
3. 根据当前环境部署的场景，获取对应的“审计结果定位”章节。查找对应审计报告名称的处理方式，并按之处理审计项。
- Region Type I：
KVM虚拟化（被级联层）：审计结果定位
- Region Type II&Region Type III：
KVM虚拟化：审计结果定位
4. 根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。
- Region Type I：
KVM虚拟化（被级联层）：手动审计
- Region Type II&Region Type III：
KVM虚拟化：手动审计
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 请联系技术支持工程师协助解决。