# 5.2.3.1.95 ALM-1101315 主机组内本地直通盘使用率超过阈值

##### 告警解释
OpenStack周期性（默认5分钟）检测主机组内本地直通盘使用率，当检测到本地直通盘使用个数达到主机组内本地直通盘总个数的85%时，系统产生此告警。
当检测到本地直通盘使用个数降低到主机组内本地直通盘总个数的70%时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1101315 | 提示 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机组ID：产生告警的主机组ID |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务<br>主机组名称：产生告警的主机组名称<br>总量：主机组内本地直通盘的总量<br>使用量：主机组内本地直通盘使用数量<br>阈值：主机组内本地直通盘的告警阈值 |
##### 对系统的影响
可能会造成之后创建本地直通盘虚拟机时，由于本地直通盘资源不够而创建失败。
##### 可能原因
主机组内本地直通盘使用率过高。
##### 处理步骤
1. 通过告警信息得到本地直通盘类型，在服务器上新增与原有本地直通盘类型相同的本地直通盘进行扩容，进行本地直通盘配置，或者释放掉多余的占用本地直通盘的虚拟机以释放资源。
参考用户指南中对应Region Type的“密集存储型云服务器配置方案”章节，进行本地直通盘配置。
2. 当本地直通盘使用率低于70%时，告警是否恢复。
- 是，处理完成。
- 否，执行3。
3. 如果上述操作出现问题，请联系技术支持工程师协助解决。
##### 参考信息
无。