# ********.34 0x101000C90004 系统配置数据所占空间已超出阈值

##### 告警解释
在Manager或Server（IP：[Server_id]）上，系统配置数据所占空间已超出阈值（80%）。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x101000C90004 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Server_id | eBackup服务器IP地址。 |
##### 对系统的影响
不涉及。
##### 可能原因
在Manager或Server上，系统配置数据所占空间已超出阈值（80%）。
##### 处理步骤
- 可能原因1：在Manager或Server上，系统配置数据所占空间已超出阈值（80%）。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至“root”用户登录。
root帐号的默认密码为*****。
- 执行“df -h” 命令查看/opt分区空间使用率是否超过80%。
- 是，执行1.d。
- 否，请联系技术支持工程师协助解决。
- 执行“cd /opt”命令进入“/opt”目录。
- 保留“/opt”目录下的huawei-data-protection，logstash，omm，root，dsware，lost+found，software，export_alarms目录，删除用户确认无用的文件或者目录，确保“/opt”分区空间使用率不超过75%。
##### 参考信息
无