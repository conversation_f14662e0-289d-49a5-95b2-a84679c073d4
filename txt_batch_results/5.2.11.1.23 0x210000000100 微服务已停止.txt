# ********.23 0x210000000100 微服务已停止

##### 告警解释
微服务（名称：[Name]，IP：[IP_Address]，端口：[Port]）已停止。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x210000000100 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Name | 微服务的名称。 |
| IP_Address | 微服务的IP地址。 |
| Port | 微服务的端口。 |
##### 对系统的影响
该微服务无法正常运行，可能导致系统整体性能下降。
##### 可能原因
微服务已停止运行。
##### 处理步骤
- 可能原因1：微服务已停止运行。
- 使用PuTTY，通过告警上报的IP地址登录已停止运行的微服务所在服务器。
默认帐户：hcp，默认密码：*****
- 执行su root命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行ps -ef | grep 微服务名称命令，检查微服务进程是否存在。
- 是，执行1.f。
- 否，执行1.d。
- 执行netstat -lp | grep 微服务端口号命令，检查该微服务相关端口是处于“LISTEN”状态。
- 是，执行1.e。
- 否，执行1.f。
- 找到“LISTEN”状态后面的进程ID，执行kill -9 进程ID命令，释放该端口。
- 执行cd /opt/huawei-data-protection/ebackup/microservice/ebk_xxx/script命令，进入微服务脚本目录其中，“ebk_xxx”是微服务名称。
- 执行source ebackup_env.sh命令，导入环境变量。
- 执行stop命令，停止该微服务。等待1分钟左右，微服务会自动启动。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无