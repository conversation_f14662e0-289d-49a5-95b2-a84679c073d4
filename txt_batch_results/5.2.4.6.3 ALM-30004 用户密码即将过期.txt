# 5.2.4.6.3 ALM-30004 用户密码即将过期

##### 告警解释
当三方系统接入用户密码和admin用户密码的剩余有效天数，小于或等于系统设置的“密码到期提前提示天数”时上报该告警。三方系统接入用户密码和admin用户密码修改后，该告警自动清除。
- 如果admin用户处于锁定状态，当admin用户的密码即将过期时不会产生该告警。
- 用户密码剩余最短时间可在“密码策略”中设置：
- 在主菜单中选择“系统管理 > 安全管理 > 安全策略”。
- 在左侧导航树中选择“密码策略”。
- 设置“密码到期提前提示天数”。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 30004 | 重要 | 时间域告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 用户名 | 密码即将过期的用户名称。 |
##### 对系统的影响
该告警发生时，如果不及时处理，密码过期后，用户将无法登录ManageOne运维面。
##### 可能原因
- 用户密码的剩余有效天数小于等于密码策略中设置的“密码到期提前提示天数”。
- 用户密码的剩余有效天数小于等于创建用户时设置的“密码到期提前提示天数”。
##### 处理步骤
1. 查看告警“定位信息”字段的值，确认密码即将过期的用户为三方系统接入用户还是admin用户。
- 三方系统接入用户密码即将过期，执行2。
- admin用户密码即将过期，执行3。
2. 联系安全管理员重置密码。
- 使用安全管理员帐号登录ManageOne运维面。
- 在主菜单中选择“系统管理 > 安全管理 > 用户管理”。
- 在左侧导航树中选择“用户”。
- 在“用户”列表中，单击密码即将过期的三方系统接入用户“操作”列的“重置密码”，重置用户密码。
- 重置密码后，执行6。
3. 使用admin用户登录ManageOne运维面。
4. 在主菜单中选择“系统管理 > 个人设置”。
5. 在“修改密码”页面输入“旧密码”、“新密码”和“确认密码”，单击“应用”。
6. 1小时后，查看本告警是否清除。
- 是，告警处理结束。
- 否，请收集上述告警处理过程中的信息，联系华为技术支持工程师。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。