# 5.2.3.1.109 ALM-1301021 EVS/OVS-DPDK 转发能力达到瓶颈告警

##### 告警解释
在EVS/OVS-DPDK转发能力达到最大转发能力时上报EVS/OVS-DPDK转发能力达到瓶颈告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1301021 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 名称 | 告警的名称。 |
| 对象ID | 产生告警对象的ID。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的ID，比如：主机ID。如果是业务模块产生的告警，则表示的是业务模块的ID，比如：Openstack OM。 |
| 对象类型 | 产生告警的对象的类型，比如：host（代表主机），heart beat（代表心跳检测系统）。 |
| 对象名称 | 产生告警的对象的名称。<br>如果是主机、虚拟机之类的资源对象产生的告警，则表示的是具体资源的名称，比如：主机名。如果是业务模块产生的告警，则表示的是业务模块的名称，比如：Openstack OM。 |
| 部件类型 | 产生告警的部件的类型，比如：Openstack、Openstack OM。 |
| 部件名称 | 产生告警的部件的名称。 |
| 定位信息 | 产生告警的对象的具体信息，一般用key=value,key=value方式。比如：host=*****,portName=###，表示：****主机上的###端口产生此告警。 |
| 附加信息 | 产生告警的一些扩展信息，一般用key=value,key=value方式。比如：Threshold=70,current_value=75，标识产生告警的原因是阈值为70，而当前值为75。 |
| 产生时间 | 告警产生的时间。 |
| 清除时间 | 告警清除的时间。 |
| 清除类型 | 告警清除的方式，告警清除的类型包括手工清除和自动清除。 |
| 清除用户 | 手工清除告警的用户名称，自动清除告警时此参数为空。 |
| 清除用户ID | 手工清除告警的用户ID，自动清除告警时此参数为空。 |
| 清除来源 | 手动清除告警时，清除告警的来源（租户侧视图或管理侧视图）。 |
| 流水号 | 此条告警信息产生的顺序编号。 |
##### 对系统的影响
虚拟网络端口出现丢包，影响业务。
##### 可能原因
- 虚拟机交换机uplink端口流量大于EVS/OVS-DPDK不丢包性能规格。
- 虚拟交换网络中广播流量过大。
- 软中断分布不均衡，长时间集中在一个CPU上，后续报文得不到及时处理。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 通过执行以下命令分别查看EVS/OVS-DPDK物理网口和网卡组bond模式的uplink端口。
ovs-vsctl --timeout=120 find interface type=dpdkphy | grep ^name | awk -F ':' '{print $2}' | tr -d '"'
ovs-vsctl --timeout=120 find interface type=dpdkbond | grep ^name | awk -F ':' '{print $2}' | tr -d '"'
6. 通过以下命令查看5查询得到的uplink端口所在的网桥。
ovs-vsctl port-to-br port_name
其中，port_name为5中查询到的端口信息。
7. 执行以下命令获取端口在6得到的网桥上的ofproto端口号。
ovs-ofctl show br_name
其中，br_name为6中的查询结果。
8. 执行以下命令得到10秒钟内uplink端口上收包数变化，计算出uplink端口的pps并与性能规格做比较，确认流量是否大于等于性能规格。
ovs-ofctl dump-ports br_name;sleep 10;ovs-ofctl dump-ports br_name
其中，br_name为6中的查询结果。
- 是，说明主机外部进入主机的流量过大，超过性能规格，请排查外部网络流量过大的原因。
- 否：
- EVS网桥，执行9。
- OVS-DPDK网桥，执行10。
9. 执行以下命令查看是否持续存在异常的广播丢包。
watch -d -n 1 ovs-appctl dpif-dpdk/show-mac-entry br_name --missed
其中，br_name为6中的查询结果。
- 是，说明存在大量广播包影响转发性能导致上报告警，由于产生异常广播流量有很多种原因，请联系技术支持工程师，根据流的走向排查异常广播流量的根源。
- 否，执行10。
10. 请联系技术支持工程师协助解决。
##### 参考信息
无。