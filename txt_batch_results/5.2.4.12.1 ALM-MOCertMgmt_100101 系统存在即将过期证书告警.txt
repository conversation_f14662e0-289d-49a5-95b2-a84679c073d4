# 5.2.4.12.1 ALM-MOCertMgmt_100101 系统存在即将过期证书告警

##### 告警解释
当ManageOne系统中存在即将过期的证书时（证书过期时间小于30天），上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| MOCertMgmt_100101 | 紧急/重要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 证书名称 | 即将过期的证书名称以及距离过期的时间 |
| 所属部件 | 证书所属的部件名称 |
##### 对系统的影响
当系统中证书过期时会对业务产生严重影响，甚至导致业务中断，需要及时更换证书。
##### 可能原因
系统中的证书即将过期，过期时间小于30天为重要告警，小于7天为紧急告警。
##### 处理步骤
1. 打开告警对应的“告警详情”，查看产生告警的证书名称和所属部件。
2. 根据证书过期场景进行处理。
- 如果证书名称为“ManageOne-PKI”且证书已过期，则参考手动恢复FusionSphere-Keystone PKI证书进行手工恢复PKI证书。
- 如果证书名称为“FusionSphere”开头的证书，查看系统中是否有“ALM-9915 证书过期预警”，如果有，则参考ALM-9915 证书过期预警处理；如果没有，则参考3-6。
- 其他场景请参考3-6进行证书更新。
3. 在菜单中选择“运维工具箱 > 统一证书”，进入证书管理页面。
4. 在“地域选择”和“部件选择”中选择需要更新的证书所在的地域和部件。
5. 勾选需要更新的证书名称，单击证书名称对应的“更新”。
6. 查看"更新状态"列显示“成功”则结束操作。否则，请再次尝试更新或联系技术支持协助处理。
##### 告警清除
当即将过期的告警被成功替换后，用户可手动清楚告警或等待系统自动清除告警。
##### 参考信息
无