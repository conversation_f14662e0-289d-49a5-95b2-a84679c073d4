# *******.17 ALM-9902 Service OM双机心跳中断

##### 告警解释
系统一分钟检测一次主备心跳IP是否能连通，不能连通则产生告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9902 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM |
| 附加信息 | 本端地址：ServiceOM主节点IP地址<br>对端地址：ServiceOM备节点IP地址 |
##### 对系统的影响
Service OM主备节点数据库及文件系统无法保证主备同步；当主节点故障时，系统可能无法倒换到备节点。
##### 可能原因
- 备节点下电。
- 网络故障。
- 备节点操作系统异常。
- 备节点HA进程未启动。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 在Service OM界面，选择“资源 > 计算资源 > 虚拟机”。
3. 在右侧搜索框输入“allinone”后单击搜索，搜索Service OM虚拟机，查看虚拟机是否都处于运行状态。
- 是，如下图，执行9。
- 否，执行4。
4. 查看界面“操作”列，启动按钮是否被灰化。
- 是，执行6。
- 否，执行5。
5. 单击“更多 > 启动”，启动Service OM虚拟机，是否能启动成功。
- 是，执行9。
- 否，执行6。
6. 使用“PuTTY”，以fsp用户通过反向代理IP登录服务器。默认密码为“*****”。
7. 执行如下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
“root”用户的默认密码为“*****”。
8. 导入环境变量后，执行以下命令，重启Service OM备节点虚拟机。
nova reboot FM_ID
FM_ID为Service OM备节点虚拟机的ID，在“资源 > 计算资源 > 虚拟机”页签，可以查看虚拟机的ID。
9. 等待约15分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行10。
10. 获取备节点虚拟机所在的主机信息，在OM界面的主机列表检查主机状态是否正常。
- 是，登录FusionSphere OpenStack安装部署界面，选择“云化服务 > FusionSphere Openstack OM”，如果虚拟机的“启动源”是“后端存储”，执行13。
- 否，执行11。
11. 检查主机是否下电。
- 是，对主机上电，等待约20分钟，如果告警自动清除，处理完毕；如果告警没有自动清除，执行12。
- 否，执行12。
12. 检查是否执行过“更换主机”操作。
- 是，执行14。
- 否，执行13。
13. 检查虚拟机所在的后端存储对接是否正常。
- 是，执行14。
- 否，恢复存储链接后，等待约15分钟，查看告警是否清除，如果告警自动清除，处理完毕；如果告警没有自动清除，执行14。
14. 参考主备部署时单个Service OM虚拟机故障恢复该虚拟机。
15. 等待约15分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行16。
16. 请联系技术支持工程师协助解决。
##### 参考信息
无。