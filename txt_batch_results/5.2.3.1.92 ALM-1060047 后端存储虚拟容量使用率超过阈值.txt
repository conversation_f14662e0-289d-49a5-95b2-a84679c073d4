# 5.2.3.1.92 ALM-1060047 后端存储虚拟容量使用率超过阈值

##### 告警解释
OpenStack周期（默认为5分钟）检测存储占用率，当检测到虚拟存储容量占用率大于85%时，系统产生此告警。
- 本告警仅针对FusionSphere OpenStack本身所使用的存储，只支持华为磁阵、FusionStorage，FusionCompute或vCenter中的存储是否上报阈值告警请关注各自管理软件。
- 虚拟存储容量=物理容量*瘦分配比
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1060047 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称 |
| 附加信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务名<br>域名：产生告警的后端存储所属Region<br>后端存储：产生告警的后端存储名称<br>阈值：存储虚拟容量使用率阈值<br>使用率：产生告警的后端存储的实际虚拟容量使用率 |
##### 对系统的影响
当虚拟存储使用率过高时，可能导致虚拟存储容量不足，系统无法使用该存储资源。
##### 可能原因
- 瘦分配比设置过小，导致物理容量充足的情况下，虚拟容量不足。
- 后端存储物理容量不足。
##### 处理步骤
1. 检查告警列表中是否存在ALM-6025 存储使用率超过阈值。
- 是，参考ALM-6025 存储使用率超过阈值处理。完成后执行2。
- 否，执行3。
2. 查看告警列表中，此告警是否恢复。
- 是，处理完成。
- 否，执行3。
3. 通过告警定位信息，获取后端存储名称。
4. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
5. 进入“配置 > 资源池管理” 页面。
6. 在资源池界面中单击，进入“集群管理”界面，查找到上报告警的后端存储名称。
7. 单击，进入“配置存储集群”界面，从“存储资源池名称”中获取存储池名称，从REST URL项中获取存储设备地址。
8. 根据存储类型，分别参考对应版本的文档进行扩容。
- 若是FusionStorage，请参考《FusionStorage V100R006C30SPC500 块存储服务容量调整指南》。
- 若是华为SAN存储，请联系技术支持协助，对存储池进行扩容。
9. 查看告警列表中，此告警是否恢复。
- 是，处理完成。
- 否，执行10。
10. 请联系技术支持工程师协助解决。
##### 参考信息
无。