# ********.65 0x10E01C0011 ibase服务异常

##### 告警解释
在HA节点（IP：[Node_Name]）上ibase服务异常。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0011 | 次要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| Node_Name | HA节点IP地址。 |
##### 对系统的影响
可能造成业务中断。
##### 可能原因
- ibase端口被占用。
- httpd启动失败。
##### 处理步骤
- 可能原因1：ibase端口被占用。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“netstat -anp | grep 8989”命令，查看执行结果中8989端口是否处于“LISTEN”状态。
- 是，执行1.d。
- 否，执行2。
- 找到“LISTEN”状态后面的进程ID，执行“kill -9 进程ID”，释放8989端口。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，执行2。
- 可能原因2：httpd端口被占用。
- 使用PuTTY，通过告警上报的服务器的IP地址登录eBackup服务器。
默认帐户：hcp，默认密码：*****
- 执行“su root”命令，输入root用户密码，切换至root用户登录。
root帐号的默认密码为*****。
- 执行“netstat -anp | grep 8080”和“netstat -anp | grep 8087”命令，查看执行结果中8080和8087端口是否处于“LISTEN”状态
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 找到“LISTEN”状态后面的进程ID，执行“kill -9 进程ID”，释放8080和8087端口。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无