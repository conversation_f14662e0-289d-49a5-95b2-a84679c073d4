# 5.2.4.18.2.2 ALM-152 网管服务异常退出告警

##### 告警解释
当部署面检测（检测周期为30秒）到某个业务进程异常退出后，连续10次重启进程失败时，产生该告警。当部署面检测到进程启动后，该告警会自动清除。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 152 | 重要 | 处理错误告警 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 服务器名 | 产生告警的节点名称 |
| 服务代理 | 产生告警的进程名称 |
| 服务名 | 产生告警的服务实例名称 |
| 站点名称 | 产生告警的站点名称。 |
##### 对系统的影响
进程退出后相关业务功能不可用，并且影响依赖相关功能的服务。
##### 可能原因
- 人为操作导致，例如强行终止了某进程。
- 系统资源不足。
##### 处理步骤
1. 查看告警参数中“服务器名”的节点是否属于“CloudSOP-UniEP”。
- 使用浏览器登录ManageOne部署面。
登录地址：https://ManageOne部署面浮动IP地址:31945。例如，https://***********:31945。
默认帐号：admin，默认密码：*****
- 在登录界面输入用户名admin和密码，单击“登录”。
- 在主菜单中选择“产品 > 系统监控”。
- 在“系统监控”页面左上方，光标移至并选择“CloudSOP-UniEP”。
- 在“节点”页签中查看是否存在告警参数中“服务器名”对应的节点名称。
- 是，只需执行2。
- 否，只需执行3。
2. 启动部署面服务。
- 使用PuTTY工具以sopuser用户通过SSH方式登录部署节点。
sopuser的默认密码为*****。
- 执行以下命令，切换到ossadm用户。
su - ossadm
ossadm的默认密码为*****。
- 如果部署面的部署模式是集群场景，请按照先Management0节点再Management1节点的顺序分别执行以下命令，启动OMMHA进程。
> cd /opt/oss/manager/agent/bin
> bash ipmc_adm -cmd startapp -app OMMHAService -tenant manager
系统提示如下回显信息，进程提示“success”，则说明OMMHA进程启动成功。否则请联系华为技术支持工程师。
Starting process ommha-0-0 ... success
- 执行以下命令，启动部署面服务。
> cd /opt/oss/manager/agent/bin
> bash ipmc_adm -cmd startapp -tenant manager
系统提示如下类似回显信息，所有进程都提示“success”，则说明部署面服务启动成功。否则请联系华为技术支持工程师。
Starting process backupwebsite-0-0 ... success
Starting process smapp-0-0 ... success
Starting process cron-0-0 ... success
...
- 查看告警是否清除。
- 是，处理完毕。
- 否，请联系华为技术支持工程师处理。
3. 确保服务进程已启动。
- 在“系统监控”页面左上方，光标移至并选择“ManageOne”。
- 在“节点”页签中，单击产生告警的节点名称。
- 在“进程”页签中，查看服务进程是否为“正在运行”状态。
- 是，执行3.d。
- 否，勾选待启动的进程，单击“启动”，启动异常进程。
- 查看告警是否清除。
- 是，处理完毕。
- 否，请联系华为技术支持工程师处理。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
以下情况会导致之前产生的该告警无法自动被清除，需要手工清除。
4. 产生该告警的节点名称发生了变化。
5. 产生该告警的站点名称发生了变化。
6. 产生该告警的服务器不被监控了。
##### 参考信息
无。