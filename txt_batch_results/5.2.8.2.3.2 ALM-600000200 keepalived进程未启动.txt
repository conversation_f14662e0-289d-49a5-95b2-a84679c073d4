# *******.3.2 ALM-600000200 keepalived进程未启动

##### 告警解释
当被监控的OBS LVS节点keepalived进程未启动的时候，上报此警告。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000200 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
影响OBS LVS服务的正常使用。
##### 可能原因
keepalived进程未启动。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 当前告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”工具，通过4中获取的节点IP地址登录产生告警的节点。
默认帐号：udslvs，默认密码：*****。
6. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
su - root
7. 执行以下命令重启keepalived服务。
systemctl restart keepalived.service
8. 执行以下命令查看keepalived服务状态。
systemctl status keepalived.service
- 出现提示Active: active (running)，表示重启keepalived服务成功，请执行9。
- 出现提示Active: inactive (dead)，表示重启keepalived服务失败，联系技术支持工程师。
9. 执行以下命令查看crond服务状态。
systemctl status crond.service
- 出现提示Active: active (running)，表示crond服务正常，请执行12。
- 出现提示Active: inactive (dead)，表示crond服务不正常，请执行10。
10. 执行以下命令重启crond服务。
systemctl restart crond.service
11. 再次执行以下命令查看crond服务状态。
systemctl status crond.service
- 出现提示Active: active (running)，表示重启crond服务成功，请执行12。
- 出现提示Active: inactive (dead)，表示重启crond服务失败，联系技术支持工程师。
12. 执行以下命令查看crond服务的配置。
cat /etc/crontab
SHELL=/bin/bash
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=root
# For details see man 4 crontabs
# Example of job definition:
# .---------------- minute (0 - 59)
# |  .------------- hour (0 - 23)
# |  |  .---------- day of month (1 - 31)
# |  |  |  .------- month (1 - 12) OR jan,feb,mar,apr ...
# |  |  |  |  .---- day of week (0 - 6) (Sunday=0 or 7) OR sun,mon,tue,wed,thu,fri,sat
# |  |  |  |  |
# *  *  *  *  * user-name  command to be executed
*/1 * * * * root sh /opt/obs/script/lvs/lvsMonitor.sh >> /dev/null 2>&1
*/1 * * * * root sh /opt/obs/script/lvs/nicoffMonitor.sh >> /dev/null 2>&1
*/1 * * * * root sh /opt/obs/script/lvs/vipMonitor.sh >> /dev/null 2>&1
*/1 * * * * root /home/<USER>/bin/manual/mstart.sh
*/2 * * * * root /opt/moopsagent/python-1.6.13/bin/python3.7 /opt/moopsagent/agent_watchdog.py >/dev/null 2>&1
- 定时执行lvsMonitor.sh脚本时间阀值为一分钟则表示配置正确，请执行15。
- 定时执行lvsMonitor.sh脚本时间阀值不为一分钟则表示配置不正确，请执行13。
13. 执行以下命令将定时执行lvsMonitor.sh脚本时间阀值修改为一分钟，保存退出。
vim /etc/crontab
SHELL=/bin/bash
PATH=/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=root
# For details see man 4 crontabs
# Example of job definition:
# .---------------- minute (0 - 59)
# |  .------------- hour (0 - 23)
# |  |  .---------- day of month (1 - 31)
# |  |  |  .------- month (1 - 12) OR jan,feb,mar,apr ...
# |  |  |  |  .---- day of week (0 - 6) (Sunday=0 or 7) OR sun,mon,tue,wed,thu,fri,sat
# |  |  |  |  |
# *  *  *  *  * user-name  command to be executed
*/1 * * * * root sh /opt/obs/script/lvs/lvsMonitor.sh >> /dev/null 2>&1
*/1 * * * * root sh /opt/obs/script/lvs/nicoffMonitor.sh >> /dev/null 2>&1
*/1 * * * * root sh /opt/obs/script/lvs/vipMonitor.sh >> /dev/null 2>&1
*/1 * * * * root /home/<USER>/bin/manual/mstart.sh
*/2 * * * * root /opt/moopsagent/python-1.6.13/bin/python3.7 /opt/moopsagent/agent_watchdog.py >/dev/null 2>&1
14. 执行以下命令重启crond服务。
systemctl restart crond.service
15. 完成keepalived服务重启和crond服务配置后，查看告警是否在下一个监控周期（约3分钟后）消除。
- 是，告警消除，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
