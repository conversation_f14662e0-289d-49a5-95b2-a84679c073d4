# 5.2.11.1.87 0x2010E01A001D 复制任务失败

##### 告警解释
复制任务（[TaskID]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x2010E01A001D | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| TaskID | 复制任务ID。 |
##### 对系统的影响
复制任务失败。
##### 可能原因
- 存储单元不可访问。
- 出现内部错误。
##### 处理步骤
- 可能原因1：存储单元不可访问。
- 在当前告警界面查看ID为0x10E00E0000（连接存储单元失败）或者0x10E00E0001（访问存储单元失败）的告警是否上报。
- 是，处理1.a所述的告警，保证存储单元可访问。
- 否，执行2。
- 可能原因2：出现内部错误。
请联系技术支持工程师协助解决。
##### 参考信息
无。