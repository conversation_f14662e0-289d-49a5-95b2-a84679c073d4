# 5.2.8.2.1.3 ALM-600000100 OBS Console tomcat证书异常

##### 告警解释
当被监控的OBS Console Tomcat证书有效期即将过期，或者已过期，或者监控异常时，上报此警告。
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 600000100 | 紧急 | 是 |
##### 告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 区域 | 产生告警信息的区域名称。 |
| 定位信息 | 云服务 | 产生告警信息的云服务名称。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 模板ID | 监控模板唯一ID。 |
| 附加信息 | 指标名称 | 产生告警信息的指标名称。 |
| 附加信息 | 监控类型 | 业务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的监控数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
##### 对系统的影响
影响OBS Console服务的正常使用。
##### 可能原因
- OBS Console Tomcat证书有效期即将过期。
- OBS Console Tomcat证书有效期已过期。
- OBS Console Tomcat证书监控异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
默认帐号：admin，默认密码：*****。
2. 在页面上方选择“运维工具箱 > 服务监控”，进入服务监控页面。
3. 在左侧导航栏选择“实例列表”，在页面右上方搜索栏中搜索参数“OBS”。
4. 单击待查看实例所在行的实例名称，进入实例监控页面。
5. 在左侧导航栏选择“keystore”，查看当前节点OBS Console Tomcat证书监控状态值。
- 值为1时，表示证书有效期即将过期，请执行6。
- 值为2时，表示证书有效期已过期，请执行6。
- 值大于等于3时，表示证书有效期监控异常，请联系技术支持工程师协助解决。
6. 执行Tomcat证书替换操作，具体操作请参见更换OBS Console Tomcat的TLS认证证书。
7. 证书替换成功后，告警是否在下一个监控周期（约3分钟后）消除。
- 是，告警消除，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。
