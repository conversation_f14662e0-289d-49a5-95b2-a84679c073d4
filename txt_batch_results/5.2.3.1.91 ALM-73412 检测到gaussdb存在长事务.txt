# 5.2.3.1.91 ALM-73412 检测到gaussdb存在长事务

##### 告警解释
gaussdb中事务执行时间如果过长（超过60分钟），比如事务未提交，则会产生告警。长事务会自动清理，但是告警产生后，需要手动清理告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73412 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的组件名称。<br>主机ID：产生告警的主机ID。 |
| 附加信息 | 详细信息：告警的详细信息。<br>主机名：产生告警的主机名。<br>云服务：产生告警的云服务。<br>微服务：产生告警的组件名称 |
##### 对系统的影响
此告警产生时，gaussdb中存在长事务。如果长事务一直存在，可能会引起表膨胀和连接占满，导致服务访问数据库异常。长事务系统会自动清理，需要排查系统是否受到长事务影响。
##### 可能原因
- 事务未提交。
- 操作系统负载高。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 确认数据库访问地址。
执行命令：
cat /etc/gaussdb/gaussdb/fsp_gaussdb.ini | grep float_ip
查看执行结果：
- 若执行成功，则输出应类似 float_ip = ************，执行9。
- 若执行失败，执行12。
9. 登录数据库。
执行命令:
su - gaussdba -c "gsql dbname -U openstack -W db_passwd -h host_ip -p 5432;"
查看执行结果：
- 若显示 dbname=>，表示进入数据库命令行交互模式，执行成功。继续执行10。
- 若执行失败，执行12。
db_name为上报告警的数据库名称（附加信息中的详细信息中“[]”内的值）；db_passwd为openstack访问数据库的密码，默认*****；host_ip为8中获得的float_ip值。
10. 查询长事务具体信息。
执行命令:
select datname,pid,now()-xact_start AS interval,query,usename,application_name,client_addr,client_hostname,client_port,xact_start,query_start,state FROM pg_stat_activity WHERE state in ('idle in transaction') AND now()-xact_start> interval '60 min' ORDER BY interval DESC;
查看执行结果：
DATNAME |  PID  |    INTERVAL     |       QUERY        |       ...      |        STATE
---------+-------+-----------------+--------------------+------ ... -----+---------------------
NOVA    | 27530 | 01:20:24.877987 | start transaction; |       ...      | idle in transaction
(1 row)
- 退出查询结果，输入q即可。
- 退出数据库，输入\q即可。
- 若输出无具体数据，即最后一行显示(0 row)，表示长事务已经自动清理完毕，请手动清除告警，处理完毕。
- 若输出有具体数据，参考11手动清理。
11. 如果确认需要手动清理长事务，通过执行处理步骤中10，获取长事务PID，并依次执行命令：
select pg_terminate_backend(pid);
查看执行结果：
NOVA=# select pg_terminate_backend(27530);
PG_TERMINATE_BACKEND
----------------------
t
(1 row)
- 若执行成功，则返回值为t，输出如上，处理完毕。
- 若执行失败，请联系技术支持工程师协助解决。
12. 请联系技术支持工程师协助解决。
##### 参考信息
无。