# 5.2.3.1.60 ALM-73014 系统文件完整性异常

##### 告警解释
定时执行系统文件完整性检查，检测系统关键文件是否被更改，根据检测结果上报不同的告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73014 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |
##### 对系统的影响
系统关键文件被更改，可能导致系统异常。
##### 可能原因
- 人为误操作。
- 正常更改。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 执行命令cd /opt/osfilecheck/result/切换目录。
9. 通过cat命令，查看该目录下所有结果文件是否有内容。
- 是，显示有文件被更改，如图所示，确认相关文件是否被正常更改。
- 是，执行osfilecheck -u命令更新文件完整性检查数据库，然后执行10。
如果更改是升级引入的，或者是为了解决问题或者优化性能等引入的，则认为是正常更改，其余情况认为是误操作更改。
- 若为误操作更改，则从安装相同版本的其他主机上拷贝正常的文件替换被修改文件，然后执行10。
- 否，执行10。
10. 执行osfilecheck -c命令进行文件完整性检查，查看“/opt/osfilecheck/result/”目录下的结果文件是否有内容。
- 是，执行11。
- 否，手动清除告警，处理完毕。
11. 请联系技术支持工程师协助解决。
##### 参考信息
无。