# ******* ALM-1200027 tomcat进程CPU占用率超过阈值

##### 告警解释
系统每隔2分钟检查一次节点tomcat进程cpu占用是否超过设定的阈值，如果在30分钟内持续超过阈值，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200027 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警信息的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
系统时间不同步，影响计费及问题定位，需尽快处理异常。
##### 可能原因
- tomcat进程运行异常。
- 服务容量不够，处理能力不足，无法满足业务需求。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”。
3. 在页面上方的菜单栏，选择“集中告警”。
4. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
5. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
6. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行下面命令，获取tomcat进程pid。
ps -ef | grep vpc | grep tomcat
回显如下所示。其中，可获取tomcat进程pid为17600。
[vpc@PUB-SRV01 ~]# ps -ef | grep vpc | grep tomcat
vpc      17600     1  6
15:35 pts/0    00:03:18
/opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
-Djdk.tls.ephemeralDHKeySize=2048
-Dorg.apache.catalina.security.SecurityListener.UMASK=0027
-Djava.endorsed.dirs=/home/<USER>/local/tomcat/endorsed -classpath
/home/<USER>/local/tomcat/bin/bootstrap.jar:/home/<USER>/local/tomcat/bin/tomcat-juli.jar
-Dcatalina.base=/home/<USER>/local/tomcat -Dcatalina.home=/home/<USER>/local/tomcat
-Djava.io.tmpdir=/home/<USER>/local/tomcat/temp
org.apache.catalina.startup.Bootstrap start
9. 执行下面命令，停止tomcat进程。
kill -9 pid
pid为8中执行命令返回信息中获取的进程id。
进程保护机制会将进程重新启动。
10. 等待5分钟，执行下面命令，检查tomcat进程是否存在。
ps -ef | grep vpc | grep tomcat
- 是，执行11。
- 否，请联系技术支持工程师协助解决。
回显如下所示时，表示tomcat进程存在。
[vpc@PUB-SRV01 ~]# ps -ef | grep vpc | grep tomcat
vpc      17600     1  6
15:35 pts/0    00:03:18
/opt/common/jre/bin/java -Dnop -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
-Djdk.tls.ephemeralDHKeySize=2048
-Dorg.apache.catalina.security.SecurityListener.UMASK=0027
-Djava.endorsed.dirs=/home/<USER>/local/tomcat/endorsed -classpath
/home/<USER>/local/tomcat/bin/bootstrap.jar:/home/<USER>/local/tomcat/bin/tomcat-juli.jar
-Dcatalina.base=/home/<USER>/local/tomcat -Dcatalina.home=/home/<USER>/local/tomcat
-Djava.io.tmpdir=/home/<USER>/local/tomcat/temp
org.apache.catalina.startup.Bootstrap start
11. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。