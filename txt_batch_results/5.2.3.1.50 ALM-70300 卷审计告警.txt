# 5.2.3.1.50 ALM-70300 卷审计告警

##### 告警解释
当执行系统审计时发现存在非正常状态的卷时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70300 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 服务名：产生告警的服务名 |
| 附加信息 | 详细信息： 卷审计产生告警的详细信息 |
##### 对系统的影响
- 此告警产生时，系统中存在状态不正常的卷，影响系统对卷的管理。
- 野卷的影响：OpenStack上查询不到对应的卷，占用后端存储空间。
- 假卷的影响：OpenStack系统中卷不可以用，占用系统资源。
- 卷残留的影响：占用存储空间。
- 中间状态卷的影响：OpenStack系统中卷不可以用，占用系统资源。
- 卷挂载状态不一致的影响：卷和虚拟机的挂载关系不一致，影响虚拟机对卷的使用。
- 级联层存在野卷的影响：被级联层的卷在级联层无法进行管理，造成被级联层资源浪费。
- 级联层存在假卷的影响：使用cinder命令查询到的卷实际上在被级联层不存在。
- 级联层卷中间态的影响：卷无法使用，占用级联层和被级联层的系统资源。
- 级联层卷与被级联层卷状态不一致的影响：系统内部数据不一致；用户对卷的操作受限。
- 级联层卷与被级联层卷挂载不一致的影响：在主机上可能残留卷挂载记录，并影响用户对卷的操作。
- 野复制对的影响：drextend数据库中查询不到该复制对，野复制对中的卷不能再创建复制对。
- 假复制对的影响：drextend数据库中存在残留复制对记录。
- 中间状态复制对的影响：复制对不可用。
- 状态不同复制对的影响：复制对主从状态不一致，影响复制对的使用。
- 野一致性复制组的影响：drextend数据库查询不到一致性复制组，野一致性复制组中的复制对不能再加入其它一致性复制组。
- 假一致性复制组的影响：drextend数据库中存在残留一致性复制组记录。
- 中间状态一致性复制组的影响：一致性复制组不可用。
- 状态不同一致性复制组的影响：一致性复制组主备状态不一致，影响一致性复制组使用。
- 复制对不同一致性复制组的影响：一致性复制组中复制对不一致，影响一致性复制组使用。
- 野双活对的影响：drextend数据库中查询不到该双活对，野双活对中的卷不能再创建双活对。
- 假双活对的影响：drextend数据库中存在残留双活对记录。
- 中间状态双活对的影响：双活对不可用。
- 野双活一致性组的影响：drextend数据库查询不到双活一致性组，野双活一致性组中的双活对不能再加入其它双活一致性组。
- 假双活一致性组的影响：drextend数据库中存在残留双活一致性组记录。
- 中间状态双活一致性组的影响：双活一致性组不可用。
##### 可能原因
- 系统中存在野卷。
- 系统中存在假卷。
- 系统中存在卷残留。
- 系统中存在处于中间状态的卷。
- 系统中存在挂载状态不正常卷。
- 系统中存在级联层存在野卷。
- 系统中存在级联层存在假卷。
- 系统中存在级联层与被级联层卷状态不一致。
- 系统中存在级联层卷中间态。
- 系统中存在级联层与被级联层卷挂载不一致。
- 系统中存在野复制对。
- 系统中存在假复制对。
- 系统中存在中间状态复制对。
- 系统中存在状态不同复制对。
- 系统中存在野一致性复制组。
- 系统中存在假一致性复制组。
- 系统中存在中间状态一致性复制组。
- 系统中存在状态不同一致性复制组。
- 系统中存在复制对不同一致性复制组。
- 系统中存在野双活对。
- 系统中存在假双活对。
- 系统中存在中间状态双活对。
- 系统中存在野双活一致性组。
- 系统中存在假双活一致性组。
- 系统中存在中间状态双活一致性组。
请在告警的详细信息中获取引发告警的具体审计问题，并参考处理步骤完成相应问题的处理。
##### 处理步骤
1. 获取告警详情中“附加信息”参数中的“详细信息”取值，此取值加.csv后缀，即为对应的审计报告名称。
2. 确定当前环境部署的场景，获取审计报告。
- Region Type I：
- 级联层：收集审计报告
- KVM虚拟化（被级联层）：收集审计报告
- 若审计报告的名称以Cascade为前缀，请参考级联层处理，否则，请参考被级联层处理。
- 如果审计类告警出现在被级联层，则无论级联层是否同时出现告警，都应当先处理被级联层告警，待告警恢复后，再次执行级联层的审计（可手动触发审计，或等待级联层每日自动进行的审计），以确认级联层与被级联层之间的信息同步。
- Region Type II&Region Type III：
- FusionCompute虚拟化：收集审计报告
- KVM虚拟化：收集审计报告
3. 根据当前环境部署的场景，获取对应的“审计结果定位”章节。查找对应审计报告名称的处理方式，并按之处理审计项。
- Region Type I：
- 级联层：审计结果定位
- KVM虚拟化（被级联层）：审计结果定位
“审计结果定位”中的审计报告名称必须与1中获取的名称完全一致。
- Region Type II&Region Type III：
- FusionCompute虚拟化：审计结果定位
- KVM虚拟化：审计结果定位
4. 根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。
- Region Type I：
- 级联层：手动审计
- KVM虚拟化（被级联层）：手动审计
- Region Type II&Region Type III：
- FusionCompute虚拟化：手动审计
- KVM虚拟化：手动审计
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。