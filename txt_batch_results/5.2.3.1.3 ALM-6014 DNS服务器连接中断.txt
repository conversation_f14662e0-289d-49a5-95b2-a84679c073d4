# 5.2.3.1.3 ALM-6014 DNS服务器连接中断

##### 告警解释
OpenStack周期（默认为1分钟）采集DNS Server对外连接状态，当连接中断时产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6014 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务<br>服务：产生告警的服务名称 |
| 附加信息 | 区域名：产生告警的域名<br>对端地址：产生告警的DNS服务器的IP地址 |
##### 对系统的影响
如果DNS Server对外连接中断，就无法访问外部的域名服务器，导致跨AZ的域名解析失败。
##### 可能原因
- 网络故障。
- 外部域名服务器故障。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
6. 执行如下操作获取外部域名服务器地址。
cps template-params-show --service dns dns-server
返回结果：
+----------+------------------------------------------+
| Property | Value                                    |
+----------+------------------------------------------+
| address  | /az1.dc1.domainname.com/**************   |
| network  | [{"ip": "***************", "systeminterf |
|          | ace": "external_api", "mask": "24", "gat |
|          | eway": "*************"}]                 |
| server   | /#/***************#53@external_api       |
+----------+------------------------------------------+
返回结果中的***************就是外部域名服务器对应的ip地址。
7. 执行如下命令，检查与外部域名服务器之间的网络是否正常。
ping 外部域名服务器地址
- 能够ping通，执行8。
- 不能ping通，执行10。
8. 检查使用外部DNS服务器是否使用bind套件实现。
- 是，执行9。
- 否，执行11。
9. 在zone配置文件中，增加“com”域配置。
例如：
zone "com" in
{
type master;
file "named.empty";
};
处理完毕。
10. 联系网络管理员排查网络故障。网络故障消除后，检查告警是否恢复。
- 是，执行结束。
- 否，执行11。
11. 联系第三方DNS服务器管理人员，确认DNS是否存在故障。
- 是，执行12。
- 否，执行13。
12. 联系第三方DNS服务器管理人员消除第三方DNS服务器故障。DNS故障消除后，检查告警是否恢复。
- 是，执行结束。
- 否，执行13。
13. 请联系技术支持工程师协助解决。
##### 参考信息
如果恢复第三方DNS服务器之后，由于DNS缓存，导致FusionSphere OpenStack系统DNS解析功能未恢复，请参考如何清理DNS缓存章节清理FusionSphere OpenStack系统中的DNS缓存。