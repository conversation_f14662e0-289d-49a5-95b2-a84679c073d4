# ********.94.1 登录eBackup服务器

登录eBackup-Manager或eBackup-Workflow节点
- 使用浏览器，登录Manager的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在导航栏单击“监控 > 服务器”。
在“服务器”页面可看到eBackup-Manager和eBackup-Workflow的IP地址。
- eBackup-Manager的管理平面IP地址为：角色为“备份管理服务器（主）”对应的“备份管理平面IP地址”。
- eBackup-Workflow（eBackup-Manager的备节点）的管理平面IP地址为：角色为“备份管理服务器（备）”对应的“备份管理平面IP地址”。
- 其他eBackup-Workflow的管理平面IP地址为：角色为“备份流程服务器”对应的“备份管理平面IP地址”。
- 使用PuTTY，通过2中获取的管理平面IP地址登录eBackup-Manager或eBackup-Workflow。
默认帐户：hcp，默认密码：*****
登录Server或Proxy节点
- 使用浏览器，登录Server的GUI。
登录地址：https://datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 在导航栏单击“监控 > 服务器”。
在“服务器”页面可看到Server和Proxy的IP地址。
- Server的管理平面IP地址为：角色为“备份服务器（主）”对应的“备份管理平面IP地址”。
- Proxy（Server的备节点）的管理平面IP地址为：角色为“备份服务器（备）”对应的“备份管理平面IP地址”。
- 其他Proxy的管理平面IP地址为：角色为“备份代理”对应的“备份管理平面IP地址”。
- 使用PuTTY，通过2中获取的管理平面IP地址登录Server或Proxy。
默认帐户：hcp，默认密码：*****