# 5.2.3.1.111 ALM-1316001 忽略升级的单板未处理

##### 告警解释
upg-server定时（默认每天一次）检查是否存在忽略升级的故障节点，如果当前升级工程已经提交且存在忽略升级的故障节点，则系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1316001 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机名：产生告警的主机名。<br>错误信息：告警相关的错误信息。 |
##### 对系统的影响
系统运行无影响，但是会影响下一次的升级。下次升级之前，必须重装执行忽略操作的故障节点，避免在下一次升级时出现残留数据、服务版本不匹配等导致升级失败的问题。
##### 可能原因
升级或回退期间，由于主机硬件故障导致升级或回退失败，并且硬件故障无法恢复的情况下，对故障节点使用了忽略操作。
##### 处理步骤
1. 在FusionSphere OpenStack安装部署界面，选择“运维 > 扩容”，将“自动PXE主机”设置为开启状态。
2. 手动启动故障主机，在启动过程中选择从网络启动，重装主机操作系统。
可使用服务器BMC系统的远程控制功能，或使用键盘显示器直连服务器，在服务器启动过程中手动选择启动方式为网络启动。
主机重新安装耗时约为10~15分钟。
3. 在FusionSphere OpenStack安装部署界面的“概要”界面，查看主机的安装进度，确认主机是否安装完成。
当故障主机所在行的“进度”显示100%时，表示主机安装成功。
4. 选择新安装的主机，单击“重启”。
5. 待所有忽略的节点重新安装完成且服务正常后，在Service OM界面，选择“资源 > 计算资源 > 主机”，查看忽略的节点是否处于资源隔离状态。
若处于资源隔离状态，请单击“解除隔离”进行解除资源隔离操作。若未处于资源隔离状态，“解除隔离”会置灰，无法单击。
6. 在FusionSphere OpenStack安装部署界面，选择“运维 > 扩容”，将“自动PXE主机”设置为关闭状态。
7. 使用“PuTTY”工具登录任意主机。
以fsp帐号登录，默认密码是*****。
执行su - root命令，切换到root帐户。输入root帐户密码，默认密码是*****
执行“Enter”，并导入openstack环境变量。
8. 执行如下命令，取消忽略的主机。
upgrade ext ignore-cancel --service-pkg-list "package1,package_version1" "package2,package_version2" --host-list host_id1,host_id2 --verify false
- package1、package_version1等需替换为实际升级的所有服务及对应的版本号，在告警详情的“附加信息”中，获取“错误信息=ignored packages:”后的字段。
- host_id1、host_id2等替换为实际忽略的主机id。
564DD0E0-7BE0-4479-FF87-CCDBAFBC25C5:/opt/fusionplatform/data/upgrade/upg-server # upgrade ext ignore-cancel --service-pkg-list "ceilometer,FUSIONSPHERE CEILOMETER 6.5.RC1.B040" "fusionplatform,FUSIONSPHERE FUSIONPLATFORM 6.5.RC1.B040" --host-list 564DCC89-DA70-5B6C-2A66-353A93556C70,564DFC2D-2195-A2F2-2EBF-2676F0AA459B --verify false
+--------------------------------------+-----------------+----------------------+-------+
| host-id                              | service-package | ignore-cancel-status | code  |
+--------------------------------------+-----------------+----------------------+-------+
| 564DCC89-DA70-5B6C-2A66-353A93556C70 | fusionplatform  | ignore_canceled      | 11213 |
| 564DCC89-DA70-5B6C-2A66-353A93556C70 | ceilometer      | ignore_canceled      | 11213 |
| 564DFC2D-2195-A2F2-2EBF-2676F0AA459B | fusionplatform  | ignore_canceled      | 11213 |
| 564DFC2D-2195-A2F2-2EBF-2676F0AA459B | ceilometer      | ignore_canceled      | 11213 |
+--------------------------------------+-----------------+----------------------+-------+
9. 执行如下命令，查看回显中host-list是否为空。
upgrade ext ignore-query
564DD0E0-7BE0-4479-FF87-CCDBAFBC25C5:/opt/fusionplatform/data/upgrade/upg-server # upgrade ext ignore-query
+--------------------------------------+
| host-list                            |
+--------------------------------------+
| 564DCC89-DA70-5B6C-2A66-353A93556C70 |
| 564DFC2D-2195-A2F2-2EBF-2676F0AA459B |
+--------------------------------------+
- 是，处理完毕。
- 否，说明还存在忽略的主机，继续执行8。