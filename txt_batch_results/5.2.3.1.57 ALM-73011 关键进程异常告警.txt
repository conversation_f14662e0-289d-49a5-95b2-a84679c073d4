# 5.2.3.1.57 ALM-73011 关键进程异常告警

##### 告警解释
系统周期（默认9秒）检测关键进程，当监控的进程或服务状态异常时，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73011 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机 ID：产生告警的主机ID。<br>异常进程名称：异常进程的名称。 |
| 附加信息 | 异常信息：告警异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的OMIP。 |
##### 对系统的影响
此告警产生时，可能导致系统异常，可能导致业务功能异常。
##### 可能原因
- 进程异常终止。
- 服务异常。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 执行以下命令切换目录。
cd /etc/sysmonitor/process
9. 根据告警信息中的异常进程或服务作为关键字，通过cat命令查看相关配置文件中配置的“RECOVER_COMMAND”，尝试恢复异常的进程或服务。
例如hirmd服务出现告警时，根据告警信息中提示的主机名登录该主机，进入“/etc/sysmonitor/process/”目录，以hirmd为关键字找到对应的配置文件为hirmd-monitor。该文件中“RECOVER_COMMAND”字段为“service hirmd restart”，可执行如下命令恢复hirmd服务进程。
service hirmd restart
10. 根据告警信息中的异常进程或服务作为关键字，通过cat命令查看相关配置文件中配置的“MONITOR_COMMAND”，查看进程或服务是否恢复正常。
例如hirmd服务MONITOR_COMMAND字段为“/usr/bin/serviceStatusCheck -s hirmd -b /usr/bin/hirmd -n hirmd -p /var/run/hirmd.pid”，可执行如下命令查看hirmd服务状态是否恢复正常。
/usr/bin/serviceStatusCheck -s hirmd -b /usr/bin/hirmd -n hirmd -p /var/run/hirmd.pid
再通过执行如下命令查看进程或服务是否正常。
echo $?
- 如果“echo $?”返回的值为“0”，表示进程或服务恢复正常，执行完毕。
- 如果“echo $?”返回的值为非“0”，表示进程或服务异常，执行11。
11. 请联系技术支持工程师协助解决。
##### 参考信息
无。