# 5.2.4.11.1.13 ALM-servicemonitor_os.fs.percent 硬盘使用率阈值告警

##### 告警解释
当被监控对象的硬盘使用率满足用户设定的告警上报阈值条件时，上报此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| servicemonitor_os.fs.percent | 次要 | 业务质量 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 资源名称 | 产生告警的监控对象名称（应用、应用实例等）。 |
| 资源类型 | 产生告警的资源类型。 |
| 主机IP | 产生告警的主机IP地址。 |
##### 对系统的影响
当被监控对象的磁盘使用率过高时，会导致被监控对象运行缓慢，并使得整个系统的吞吐量下降，系统响应延迟增高。
##### 可能原因
磁盘被占满。
##### 处理步骤
本处理步骤中所呈现的数值、文件、进程等信息仅作为告警消除的示例，可能与实际环境中的信息不一致，具体操作中请以实际环境信息参考如下步骤进行处理。
1. 打开告警对应的“告警详情”查看“IP地址/URL/域名”，获取告警源的主机IP地址。
2. 使用Putty工具以具有登录权限的系统用户登录上报告警的异常节点，切换到root用户。
- ManageOne节点登录root用户的方法如下。
- 使用sopuser用户登录上报告警的异常节点IP地址。
默认密码：*****
- 执行如下命令，切换root用户。
sudo su root
- 非ManageOne节点登录root用户的方法请从《华为云Stack 6.5.1 安全管理指南》“帐户管理 > 帐户一览表”中“A类”页签查找节点对应云服务的root用户登录方法。
3. 查看硬盘使用率。如果有如下异常情况，执行4，否则联系技术支持协助处理。
- 执行如下命令，查找“Use%”列显示占用率为90%或较高值对应的文件夹。如图1所示。
df -h
图1 文件占用磁盘空间查询
如果发现其他文件夹的“Use%”列显示占用率为90%或者较高，也可以按后续步骤进行处理。
- 执行如下命令，查询/opt目录下大于50M的文件。
find /opt -type f -size +50000k -exec ls -lh {} \; | awk '{ print $9 ": " $5 }'
4. 业务正常情况下，联系业务运维人员进行扩容、迁移业务，其他情况请联系技术支持工程师协助解决。
##### 参考信息
无。