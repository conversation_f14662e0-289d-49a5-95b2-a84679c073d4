# 5.2.3.1.11 ALM-6022 主机与NTP服务器心跳状态异常

##### 告警解释
ntp-client会周期性（默认为2min）检查本节点与主ntp-server所在节点的连接状态，当节点间出现网络故障或者主ntp-server出现故障时，且持续时间超过30分钟后，产生此告警。当节点间网络故障修复或者主ntp-server故障修复时，该告警消除。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6022 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：主机管理ip。<br>对端地址：ntp-server浮动ip。 |
##### 对系统的影响
- 如果ntp-client与主ntp-server所在节点网络故障或者主ntp-server故障时，则该ntp-client所在节点不与内部主ntp-server进行时间同步。
- 可能导致合法的token未超过有效期不可用或超过有效期却可用。
- 可能导致时间不同步节点服务不可用。
- 可能导致系统数据采样不正确。
- 可能导致数据丢失。
##### 可能原因
- 主ntp-server浮动ip未配置或被删除。
- 主ntp-server进程故障。
- 内部网络故障。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
6. 执行以下操作，获取ntp-server部署情况。
cps template-instance-list --service ntp ntp-server
回显如下类似信息：
+------------+---------------+---------+--------------------------------------+---------------+
| instanceid | componenttype | status  | runsonhost                           | omip          |
+------------+---------------+---------+--------------------------------------+---------------+
| 0          | ntp-server    | active  | 304BA4EF-98F8-8F94-E811-852B82CB59EF | ************* |
| 1          | ntp-server    | standby | 6A7554E9-410C-61A7-E811-6D0CD8E2B1D4 | ************* |
+------------+---------------+---------+--------------------------------------+---------------+
回显信息数目取决于ntp-server的部署方式，status显示active字段的即为主NTP Server所在的节点。
7. 根据获取到的ip信息，登录主ntp-server部署所在节点，用户名与密码参照1～2。
8. 在主ntp-server所在节点，执行以下命令。
ip addr show |grep brcps:ntp
回显如下类似信息：
inet ***********/16 scope global secondary brcps:ntp-s
浮动ip是否配置。
- 是，执行9。
- 否，执行10。
9. 执行以下命令，查看主ntp-server进程状态。
ps -ef|grep ntp |grep -v grep
回显信息类似如下：
root      5832     1  0 Aug03 ?        00:00:27 /usr/bin/python /usr/local/bin/ntp-client/ntp_client/ntpclient.py -s ***********
root     22843     1  0 01:33 ?        00:00:00 /usr/sbin/ntpd -4 -c /usr/local/bin/ntp-server/conf/ntp.conf
cps      22851     1  0 01:33 ?        00:00:10 python /usr/local/bin/ntp-server/ntp_server/ntpserver.py
进程是否存在。
- 是，执行13。
- 否，执行10。
10. 执行如下命令，停止主ntp-server进程。
cps host-template-instance-operate --service ntp --action stop ntp-server --host NTP主机ID
回显类似信息如下：
+------------+--------------------------------------+---------+
| template   | runsonhost                           | result  |
+------------+--------------------------------------+---------+
| ntp-server | CCCC8175-8F78-0000-1000-1DD200005020 | success |
+------------+--------------------------------------+---------+
是否停止进程成功。
- 是，执行11。
- 否，执行13。
NTP主机ID指的是主ntp-server节点id，即runsonhost的参数值。
11. 执行如下命令，启动主ntp-server进程。
cps host-template-instance-operate --service ntp --action start ntp-server --host NTP主机ID
回显类似信息如下：
+------------+--------------------------------------+---------+
| template   | runsonhost                           | result  |
+------------+--------------------------------------+---------+
| ntp-server | CCCC8175-8F78-0000-1000-1DD200005020 | success |
+------------+--------------------------------------+---------+
是否启动进程成功。
- 是，执行12。
- 否，执行13。
NTP主机ID指的是主ntp-server节点id，即runsonhost的参数值。
12. 等待5分钟～10分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行13。
13. 请联系技术支持工程师协助解决。
##### 参考信息
无。