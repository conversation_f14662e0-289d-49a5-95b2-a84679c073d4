# 5.2.3.1.51 ALM-70310 快照审计告警

##### 告警解释
当执行系统审计时发现存在非正常状态的快照时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70310 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 服务名：产生告警的服务名 |
| 附加信息 | 详细信息： 快照审计产生告警的详细信息 |
##### 对系统的影响
- 此告警产生时，系统中存在状态不正常的快照，影响系统对快照的管理。
- 野快照的影响：OpenStack上查询不到对应的快照，占用后端存储空间。
- 假快照的影响：OpenStack系统中快照不可以用，占用系统资源。
- 中间状态快照的影响：OpenStack系统中快照不可以用，占用系统资源。
- 级联层快照与被级联层快照不一致的影响：级联和被级联状态不一致，系统不能使用该快照。
##### 可能原因
- 系统中存在野快照。
- 系统中存在假快照。
- 系统中存在快照处于中间状态。
- 级联层快照与被级联层快照不一致。
请在告警的详细信息中获取引发告警的具体审计问题，并参考处理步骤完成相应问题的处理。
##### 处理步骤
1. 获取告警详情中“附加信息”参数中的“详细信息”取值，此取值加.csv后缀，即为对应的审计报告名称。
2. 确定当前环境部署的场景，获取审计报告。
- Region Type I：
- 级联层：收集审计报告
- KVM虚拟化（被级联层）：收集审计报告
- 若审计报告的名称以Cascade为前缀，请参考级联层处理，否则，请参考被级联层处理。
- 如果审计类告警出现在被级联层，则无论级联层是否同时出现告警，都应当先处理被级联层告警，待告警恢复后，再次执行级联层的审计（可手动触发审计，或等待级联层每日自动进行的审计），以确认级联层与被级联层之间的信息同步。
- Region Type II&Region Type III：
- FusionCompute虚拟化：收集审计报告
- KVM虚拟化：收集审计报告
3. 根据当前环境部署的场景，获取对应的“审计结果定位”章节。查找对应审计报告名称的处理方式，并按之处理审计项。
- Region Type I：
- 级联层：审计结果定位
- KVM虚拟化（被级联层）：审计结果定位
“审计结果定位”中的审计报告名称必须与1中获取的名称完全一致。
- Region Type II&Region Type III：
- FusionCompute虚拟化：审计结果定位
- KVM虚拟化：审计结果定位
4. 根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。
- Region Type I：
- 级联层：手动审计
- KVM虚拟化（被级联层）：手动审计
- Region Type II&Region Type III：
- FusionCompute虚拟化：手动审计
- KVM虚拟化：手动审计
5. 查看告警是否清除。
- 是，处理完毕。
- 否，执行6。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。