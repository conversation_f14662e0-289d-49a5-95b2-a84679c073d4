# *******.1 ALM-1150001 组合API节点NTP进程状态异常

告警解释
系统每隔60秒检查一次NTP进程是否存在，如果不存在，产生此告警。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1150001 | 重要 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 告警名称 | 告警名称 | 此条告警信息的名称 |
| 来源系统 | 来源系统 | 对接系统的名称 |
| IP地址 | IP地址 | 产生告警信息的业务服务器的IP地址 |
| 首次发生时间 | 首次发生时间 | 此条告警信息首次发生时间 |
| 定位信息 | 服务 | 产生此告警的服务名 |
| 定位信息 | 资源类型 | 产生告警的资源类型 |
| 定位信息 | 监控类型 | 产生告警的监控类型 |
| 定位信息 | 主机IP | 产生告警的主机IP |
| 定位信息 | 详细信息 | 产生告警的详细信息 |
对系统的影响
系统时间跳变会影响Job调度，严重情况下可能导致多个应用节点之间相互误判彼此健康状况，误触发宕机事件，从而导致服务不可用。
可能原因
NTP进程异常。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用PuTTY，登录4中确认的虚拟机。
默认帐号：apicom，默认密码：*****。
6. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
7. 执行如下命令，切换到root用户。
sudo su - root
8. 执行如下命令，检查NTP进程是否存在。
ps -ef |grep ntp
回显如下所示时，表示NTP进程存在。
[root@CPT-SRV01 ~]# ps -ef |grep ntp
root  612   1            0  Jan07 ?       00:02:09 /usr/sbin/irqbalance -- foreground -- policyscript=/etc/sysconfig/irqbalance.rules --hintpolicy=subnet
ntp   28076 1          0  19:42  ?       00:00:00 /usr/sbin/ntpd -u ntp:ntp -g
root  28597 26104  0  19:42 pts/3  00:00:00 grep -- color=auto ntp
- 是，执行9。
- 否，执行11。
9. 执行如下命令，重启NTP进程。
service ntp restart
10. 检查NTP进程是否重启成功。
回显如下所示时，表示NTP进程重启成功。
[root@CPT-SRV01 ~]# service ntp restart
Redirecting to /bin/systemctl restart ntp.service
- 是，执13。
- 否，请联系技术支持工程师协助解决。
11. 执行如下命令，启动NTP进程。
service ntp start
12. 检查NTP进程是否启动成功。
回显如下所示时，表示NTP进程启动成功。
[root@CPT-SRV01 ~]# service ntp start
Redirecting to /bin/systemctl start ntp.service
- 是，执行13。
- 否，请联系技术支持工程师协助解决。
13. 等待10分钟，检查告警是否已自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
参考信息
无。