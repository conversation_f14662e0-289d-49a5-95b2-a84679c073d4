# *******.3.1.3 查看cinder-scheduler日志

- Region Type I场景，使用PuTTY，以“Cascading-Reverse-Proxy”字段（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）对应的IP地址登录级联层FusionSphere OpenStack节点。
Region Type II或Region Type III场景，使用PuTTY，以“Reverse-Proxy”（在参数信息汇总文件《xxx_export_all_CN.xlsm》中搜索）字段对应的IP地址登录FusionSphere OpenStack节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，导入环境变量。
source set_env
导入过程中，请输入1，选择环境变量“openstack environment variable (keystone v3)”，默认密码为*****。
- 执行如下命令，查询所有cinder-scheduler控制节点。
cps template-instance-list --service cinder cinder-scheduler
- 通过4中查询到的IP地址依次登录所有cinder-scheduler控制节点。
默认帐号：fsp，默认密码：*****
- 执行su root命令，输入root帐号密码，切换至root帐号。
root帐号的默认密码为*****。
- 执行如下命令，在所有cinder-scheduler控制节点上，根据7中获得的request id搜索cinder-scheduler日志，根据错误发生的时间，找到ERROR字样，查看报错信息。
zgrep req_id /var/log/fusionsphere/component/cinder-scheduler/ *
- 根据报错信息分析错误产生的原因并进行处理。