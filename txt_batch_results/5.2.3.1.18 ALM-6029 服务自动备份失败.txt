# 5.2.3.1.18 ALM-6029 服务自动备份失败

##### 告警解释
backup-server定时（默认每天凌晨3点）下发自动备份命令，如果某服务自动备份失败，则上报该服务的备份组件失败告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6029 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>服务：产生告警的服务。 |
| 附加信息 | 云服务：产生告警的云服务。 |
##### 对系统的影响
系统无法完成备份，影响系统的可靠性。
##### 可能原因
- 网络故障。
- 备份分区空间不足。
##### 处理步骤
1. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
2. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
3. 执行以下命令，防止系统超时退出。
TMOUT=0
4. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
5. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
6. 执行如下操作，查看告警服务的备份结果。
backup progress-get --service all --detail
命令中all可以替换为单独的服务，如cps、mongodb等。若命令为all，则显示所有服务的备份结果详情。
回显信息分为TOTAL部分和DETAIL部分。
备份结果从回显信息TOTAL部分的“result”列数据中获取。
TOTAL:
+---------+---------------------+---------------------+---------+----------+---------+
| service | start time          | end time            | result  | progress | message |
+---------+---------------------+---------------------+---------+----------+---------+
| cbs     | 2019-04-22 03:00:01 | 2019-04-22 03:00:23 | SUCCESS | 100%     |         |
| cps     | 2019-04-22 03:00:01 | 2019-04-22 03:00:32 | SUCCESS | 100%     |         |
| gaussdb | 2019-04-22 03:00:01 | 2019-04-22 03:00:39 | SUCCESS | 100%     |         |
| mongodb | 2019-04-22 03:00:01 | 2019-04-22 03:00:46 | SUCCESS | 100%     |         |
+---------+---------------------+---------------------+---------+----------+---------+
- result为“DOING”，等待一段时间（如5分钟），重复执行该命令。
- result为“SUCCESS”，告警将会自动恢复，处理完成。
- result为“FAILED”，查看回显信息中“message”是否为“Upload backup file to third-party server failed”。
- 是，说明备份包上传第三方服务器失败，需要排查第三方服务器连接是否正常。排查故障后，执行13。
- 否，执行7。
7. 在备份结果的详细信息中，查看errorcode。
errorcode从回显信息DETAIL部分的“errorcode”列数据中获取。
DETAIL:
+---------+--------------------------------------+---------+----------+-----------+----------+
| service | id                                   | result  | progress | errorcode | errormsg |
+---------+--------------------------------------+---------+----------+-----------+----------+
| cbs     | 564D82B5-14CD-7C60-6A1E-CAC910608867 | SUCCESS | 100%     | 0         |          |
|         | 564D07D8-2D85-67F8-4B84-04E55FEEE592 | IGNORE  | 100%     | 0         |          |
| cps     | 564D49B9-5C36-9768-FA51-54102E5A6044 | SUCCESS | 100%     | 0         |          |
|         | 564D82B5-14CD-7C60-6A1E-CAC910608867 | IGNORE  | 100%     | 0         |          |
|         | 564D07D8-2D85-67F8-4B84-04E55FEEE592 | IGNORE  | 100%     | 0         |          |
| gaussdb | 564D49B9-5C36-9768-FA51-54102E5A6044 | SUCCESS | 100%     | 0         |          |
|         | 564D07D8-2D85-67F8-4B84-04E55FEEE592 | IGNORE  | 100%     | 0         |          |
| mongodb | 564D49B9-5C36-9768-FA51-54102E5A6044 | SUCCESS | 100%     | 0         |          |
|         | 564D82B5-14CD-7C60-6A1E-CAC910608867 | SUCCESS | 100%     | 0         |          |
|         | 564D07D8-2D85-67F8-4B84-04E55FEEE592 | SUCCESS | 100%     | 0         |          |
+---------+--------------------------------------+---------+----------+-----------+----------+
- errorcode为20007，执行8。
- errorcode为10004、10005、10006、20001、30002，执行10。
- errorcode为50002，查看service的取值。
- 如果service为mongodb，执行11。
- 如果service为gaussdb，执行12。
- 如果service为其他组件，执行14。
- 其他errorcode，执行14。
8. 执行如下命令，获取备份分区目录（默认为/opt/backup/backupfile）下的无用备份包。
ll /opt/backup/backupfile/service_name
命令中service_name表示故障服务，如cps、mongodb等，可从7中“service”列获取。
9. 清理磁盘空间，执行如下命令，删除8中获取的无用备份包。
backup package-delete --package package_name
删除完成后，执行13。
10. 在备份结果的详细信息中查看备份失败的主机ID/虚拟机ID，登录到相应的主机/虚拟机，排查网络故障。
排查完毕后，执行13。
11. 执行如下命令，查看备份失败的节点的mongodb组件状态是否为fault。
cps template-instance-list --service mongodb mongodb
- 是，请参考mongodb组件故障处理章节处理。
处理结束后，且mongodb组件状态恢复正常，执行13。
- 否，执行14。
12. 执行如下命令，查看备份失败的节点的gaussdb组件状态是否为fault。
cps template-instance-list --service gaussdb gaussdb
- 是，请参考gaussdb组件故障处理章节处理。
处理结束后，且gaussdb组件状态恢复正常，执行13。
- 否，执行14。
13. 执行如下命令，手动对故障服务执行备份操作。
backup execute --service service_name
等待约30分钟，查看告警是否清除。
- 是，处理完成。
- 否，执行14。
14. 请联系技术支持工程师协助解决。
##### 参考信息
无。