# 5.2.3.1.65 ALM-73019 主机进程数异常

##### 告警解释
OpenStack按照60秒周期检测主机的系统进程数，当主机的系统进程数量大于等于告警阈值（系统最大进程数的90%与/etc/sysmonitor/pscnt配置文件配置的告警值中的最大值）时，系统产生此告警。当系统进程数小于恢复阈值（系统最大进程数的80%与/etc/sysmonitor/pscnt配置文件配置的恢复值中的最大值）时，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73019 | 次要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机ID：产生告警的主机ID。 |
| 附加信息 | 异常信息：告警的异常信息。<br>主机 ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>主机IP：产生告警的主机OMIP。 |
##### 对系统的影响
进程数过高，系统可能无法创建新的进程，从而影响服务运行或无法登录进行问题定位处理。
##### 可能原因
系统异常。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 在“概要”界面，根据告警附加信息中的“主机ID”或“主机名”，在“管理IP地址”一栏获得主机的管理IP地址。
3. 使用PuTTY，通过主机的管理IP地址，登录告警所在主机。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
4. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
5. 执行以下命令，防止系统超时退出。
TMOUT=0
6. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
7. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
8. 执行以下命令，查看当前系统中目前有多少个进程。
ps -xH | sed '1d' | wc -l
564D8324-F2F0-5440-DFE7-B5B21D5323A0:/home/<USER>'1d' | wc -l
553
9. 执行以下命令，查看/etc/sysmonitor/pscnt配置文件中配置的告警上限。
cat /etc/sysmonitor/pscnt
564D8324-F2F0-5440-DFE7-B5B21D5323A0:/home/<USER>/etc/sysmonitor/pscnt
# Ceiling percentage of processes(threads) number alarm(Maximum between this value and 90% pid_max)
ALARM="1600"
# Floor percentage of processes(theads) number alarm(Maximum between this value and 80% pid_max)
RESUME="1500"
# Periodic of monitor (second)
PERIOD="60"
10. 执行以下命令，查看当前系统的最大进程数（示例中为32768，不同的机器该值不同）。
cat /proc/sys/kernel/pid_max
564D8324-F2F0-5440-DFE7-B5B21D5323A0:/home/<USER>/proc/sys/kernel/pid_max
32768
11. 确认当前系统进程个数是否已经超过了/etc/sysmonitor/pscnt配置文件中配置的告警上限与系统最大进程数的90%中的最大值。
- 是，执行12。
- 否，执行15。
12. 执行以下命令，查看具体的进程。
ps -aux
564D8324-F2F0-5440-DFE7-B5B21D5323A0:/home/<USER>
USER       PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root         1  1.6  0.0 191268  4332 ?        Ss   Nov09 172:05 /usr/lib/systemd/systemd --switched-root --system --deserialize 21
root         2  0.0  0.0      0     0 ?        S    Nov09   0:05 [kthreadd]
root         3  0.1  0.0      0     0 ?        S    Nov09  19:13 [ksoftirqd/0]
root         5  0.0  0.0      0     0 ?        S<   Nov09   0:00 [kworker/0:0H]
root         8  0.1  0.0      0     0 ?        S    Nov09  13:34 [migration/0]
root         9  0.0  0.0      0     0 ?        S    Nov09   0:00 [rcu_bh]
root        10  1.3  0.0      0     0 ?        S    Nov09 138:35 [rcu_sched]
root        11  0.0  0.0      0     0 ?        S    Nov09   1:05 [watchdog/0]
root        12  0.0  0.0      0     0 ?        S    Nov09   1:05 [watchdog/1]
root        13  0.2  0.0      0     0 ?        S    Nov09  23:28 [migration/1]
root        14  0.2  0.0      0     0 ?        S    Nov09  20:27 [ksoftirqd/1]
root        16  0.0  0.0      0     0 ?        S<   Nov09   0:00 [kworker/1:0H]
root        17  0.0  0.0      0     0 ?        S    Nov09   1:03 [watchdog/2]
13. 查看是否出现不需要的进程，或者重复的进程。
- 是，使用kill -9 PID命令来将指定pid的进程杀死，或使用pkill xxname命令将指定名字的进程杀死，或者使用killall xxname命令将所有的同名进程杀死。
其中，PID为12中给出的进程PID号，xxname为12中给出的COMMAND。
- 否，或者用户无法判断识别异常进程，执行15。
14. 待系统进程数满足告警恢复阈值后，等待1~2分钟，查看告警是否恢复。
- 是，处理完毕。
- 否，执行15。
15. 请联系技术支持工程师协助解决。
##### 参考信息
无。