# *******.14 ALM-9801 Service OM资源异常

##### 告警解释
每20秒钟检测Service OM资源状态，当连续三次检测到资源状态异常，系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9801 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>IP地址：ServiceOM虚拟机主节点的IP地址 |
| 附加信息 | 组件：异常组件信息列表<br>节点名称：ServiceOM虚拟机主节点名称 |
##### 对系统的影响
资源异常对系统影响随不同的资源而不同，附加信息中包含了异常的资源名，各资源影响如表1所示。
| 表1 资源异常影响 | 表1 资源异常影响 |
| --- | --- |
| 资源名 | 资源异常影响 |
| irm | 资源管理将不可用。 |
| uportal | Service OM界面无法登录，所有的操作界面都会异常。<br>北向接口不可用。 |
| primarydb | Service OM的操作界面所有操作可能无法直接生效。 |
| csm | 使用RPC服务的模块会受到影响，如：irm、connector等。<br>软件调测功能、License管理功能、时间管理功能不可用，后台自动定时备份将无法执行。 |
| uhm | 硬件相关的资源管理不可用。 |
| exfloatip | Service OM所有操作将无法进行。 |
| omfloatip | SNMP告警将无法提供。 |
| standbydb | Service OM的操作界面所有操作可能无法同步到备机，当主Service OM节点掉电可能导致数据丢失。 |
| arcontrol | Service OM资源管理不可用。 |
| ardata | 报表和监控将不可用。 |
| nginx | 整个系统将不可用。 |
| connector | Service OM资源池管理不可用。 |
| orchestrator | Service OM部分资源不可用。 |
| fault | 告警功能不可用。 |
| HA | Service OM主备节点数据库及文件系统无法保证同步。<br>当Service OM主节点故障时，系统可能无法倒换到备节点。 |
##### 可能原因
监控资源检测到某个资源异常后，尝试重启该资源，如果连续三次重启仍然失败将触发该告警。
##### 处理步骤
检查系统资源状态
1. 在告警列表中，单击此告警所在行，在“告警详细信息”区域“附加信息”中，查看如下信息：
- 告警对应的资源名称，如：connector
- 告警对应的节点名称，如：allinonefm0
2. 使用“PuTTY”，以“galaxmanager”用户通过管理浮动IP地址登录Service OM主节点 。
默认帐号：galaxmanager，默认密码：*****
登录FusionSphere OpenStack的安装部署界面，具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。选择“云化服务 > FusionSphere OpenStack OM”，查看OM列表即可获取管理浮动IP信息。
3. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
4. 执行以下命令，获取主备节点的节点名称和管理IP地址。
QueryHaState om
输出结果类似如下显示，表明当前节点名称为allinonefm0，当前节点为备节点，其管理IP地址是**************；对端节点名称为allinonefm1，对端节点为主节点，其管理IP地址是**************。
LOCAL_HOST=allinonefm0
LOCAL_STATE=standby
LOCAL_IP=**************
REMOTE_HOST=allinonefm1
REMOTE_STATE=active
REMOTE_IP=**************
5. 检查资源名称是否为“standbydb”。
- 是，执行6。
- 否，执行11。
6. 检查告警对应的节点名称是否为备节点的节点名称。
- 是，执行7。
- 否，执行16。
7. 使用“PuTTY”，以“galaxmanager”用户通过备节点管理IP地址登录Service OM备节点。
8. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
9. 执行以下命令，启动HA。
haStartAll -r
10. 经过约15分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行16。
11. 检查告警对应的节点名称是否为主节点的节点名称。
- 是，执行12。
- 否，执行16。
12. 使用“PuTTY”，以“galaxmanager”用户通过主节点管理IP地址登录Service OM主节点。
13. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
14. 执行以下命令，启动HA。
haStartAll -r
15. 经过约15分钟，查看告警是否清除。
- 是，处理完毕。
- 否，执行16。
16. 请联系技术支持工程师协助解决。
##### 参考信息
无。