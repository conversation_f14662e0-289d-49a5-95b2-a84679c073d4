# ********.3 ALM-48409-文件句柄使用率超过门限值

##### 告警解释
当API网关节点的文件句柄使用率过高（超过80%），系统产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 告警类型 |
| --- | --- | --- |
| 48409 | 次要 | 设备告警 |
##### 告警参数
| 分类 | 参数名称 | 参数说明 |
| --- | --- | --- |
| 定位信息 | CloudService | 云服务名称 |
| 定位信息 | Node | 故障节点IP地址 |
| 定位信息 | User_Name | 用户名 |
| 附加信 | Alarm_Reason | 告警原因 |
##### 对系统的影响
文件句柄使用率超过门限值，系统可能会无法创建进程，影响业务性能。
##### 可能原因
网关部署所在的服务器文件句柄使用率达到设置的门限值。
##### 处理步骤
1. 查看告警的定位信息。
- 以admin帐号登录ManageOne运维面。
登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。
- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。
- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。
- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。
- Node：表示告警源节点IP地址。
- User_Name：表示产生告警的用户名。
如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。
2. 使用PuTTY，登录告警源节点Node。
默认帐号： paas，默认密码：*****。
3. 执行以下命令，切换到root用户。
sudo su - root
默认密码：*****。
4. 执行以下命令，防止会话超时退出。
TMOUT=0
5. 执行以下命令，切换至User_Name用户。
su - User_Name
6. 执行以下命令，查看当前系统最大句柄设置数，并判断查询结果是否大于“2000000”。
ulimit -n
- 是 => 8
- 否 => 7
7. 执行以下步骤，将系统文件句柄数增加到“2000000”。
- 执行以下命令，切换到root用户。
exit
- 执行以下命令，修改配置文件中用户User_Name的文件句柄数为“2000000”。
命令中的User_Name需要替换为实际的用户名。
echo "User_Name soft nofile 2000000" >> /etc/security/limits.conf
echo "User_Name hard nofile 2000000" >> /etc/security/limits.conf
8. 执行以下命令，将系统文件数句柄数增加到“4000000”。
- 执行以下命令，切换到root用户。
exit
- 执行以下命令，修改配置文件中用户User_Name的文件句柄数为“4000000”。
命令中的User_Name需要替换为实际的用户名。
echo "User_Name soft nofile 4000000" >> /etc/security/limits.conf
echo "User_Name hard nofile 4000000" >> /etc/security/limits.conf
9. 等待1小时，查看告警是否清除。
- 是 => 处理完毕
- 否 => 11
10. 获取相关日志，并联系技术支持。
- 执行如下命令，切换到日志目录。
cd /var/log/apigateway/opsagent
- 下载日志“opsagent.log”到本地，并联系技术支持。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。
