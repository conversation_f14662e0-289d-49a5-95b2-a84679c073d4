# ********.50 0x10E01C0000 添加Workflow或Proxy失败

##### 告警解释
添加Workflow或Proxy（IP：[IP_addr]）失败。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x10E01C0000 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IP_addr | IP地址。 |
##### 对系统的影响
不涉及。
##### 可能原因
- Workflow（Proxy）与Manager（Server）之间的网络连接中断。
- 网络性能差。
##### 处理步骤
- 可能原因1：Workflow（Proxy）与Manager（Server）之间的网络连接中断。
- 参考登录eBackup服务器登录eBackup-Manager或Server。
- 执行“su root”命令，输入root用户密码，切换至root用户。
root帐号的默认密码为*****。
- 如果是IPv4，执行“ping Workflow或Proxy的IP地址”，如果是IPv6，执行“ping6 Workflow或Proxy的IP地址”，检查是否可以ping通。
- 是，执行2。
- 否，请联系机房管理员修复网络，确保网络连接正常。
- 可能原因2：网络性能差。
- 根据1的执行结果，查看是否存成在丢包的现象。
- 是，联系管理员提高网络性能质量，保证Workflow（Proxy）和Manager（Server）间通信稳定。
- 否，执行2.b。
- 使用浏览器，登录Manager或Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“监控 > 服务器”，进入“服务器”界面，查看该服务器是否处于可访问状态。
- 是，执行2.d。
- 否，请联系技术支持工程师协助解决。
- 等待3分钟左右，在当前告警界面查看该告警是否已恢复。
- 是，处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无