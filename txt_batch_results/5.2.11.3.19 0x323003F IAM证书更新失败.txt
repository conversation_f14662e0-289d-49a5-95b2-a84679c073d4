# ********.19 0x323003F IAM证书更新失败

##### 告警解释
从IAM服务器更新证书失败
##### 告警属性
| 告警ID | 告警级别 | 自动清除 |
| --- | --- | --- |
| 0x323003F | 警告 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| IAM IP地址或者域名地址 | IAM服务器的IP地址或者域名 |
##### 对系统的影响
更新IAM证书失败，可能导致用户token校验失败。
##### 可能原因
- IAM服务器无法连接。
- 对接IAM的用户名密码错误。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面，在“快速访问”页签单击“eReplication”，登录eReplication。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 使用PuTTY，根据1浏览器中的服务器IP地址登录到主服务器节点。
默认帐号：DRManager，默认密码：*****。
3. 执行su - root命令切换到root帐号。
root帐号的默认密码为*****。
4. 执行ping命令检查BCManager与IAM服务器间网络连接是否正常。
- 是，请转6。
- 否，请修复网络连接，请转5。
5. 修复后在BCManager界面选择“资源 > localServer > FusionSphere”界面，选择 Openstack组件，单击“刷新”按钮。检查告警是否清除。
- 是，处理结束。
- 否，请执行6。
6. 请联系技术支持工程师协助解决。
##### 参考信息
无。