# 5.2.17.12.5 ALM-2002501 站点网络状态异常

告警解释
- 站点1与站点2之间网络中断、站点1或者站点2整体失效时，系统产生此告警。
- 此告警提示系统故障或风险，需要处理。
告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 2002501 | 紧急 | 是 |
告警参数
| 参数名称 | 参数名称 | 参数含义 |
| --- | --- | --- |
| 定位信息 | 资源名称 | 产生告警信息的设备名称。 |
| 定位信息 | 资源类型 | MONITOR。 |
| 定位信息 | 主机IP | 对应主机的IP地址。 |
| 附加信息 | 监控类型 | 服务监控。 |
| 附加信息 | 详细信息 | 最近几个周期的数据。 |
| 附加信息 | 阈值条件 | 产生告警的阈值。 |
对系统的影响
系统单站点运行，存在可靠性风险。
可能原因
- 站点1与站点2之间网络中断。
- 站点1或站点2整机房下电。
- 站点1或站点2与对端站点以及站点3之间网络全部中断。
处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 在页面上方的菜单栏，选择“集中告警 > 集中告警”。
3. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
4. 在“告警详情”页的“定位信息”中获取“主机IP”，即告警节点IP地址。
5. 使用“PuTTY”，通过4中获取的节点管理IP地址登录站点1所在虚拟机。
默认帐号：arbiter，默认密码：*****。
6. 执行以下命令，并按提示输入“root”用户的密码，默认密码：*****，切换至“root”用户。
sudo su
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行以下命令查询站点1到站点2的双站点整体网络状态是否正常。
etcdctl -u arbiterClient get /monitor/status/dc001_dc002
9. 执行以下命令查询站点2到站点1的私网状态是否正常。
etcdctl -u arbiterClient get /monitor/status/internal/dc002_dc001
10. 查看私网状态是否正常。
- 私网状态异常，请执行11。
- 私网状态正常，请执行12。
11. 执行以下命令，重启monitor。
systemctl restart arbitration-monitor
- 重启成功，请重新执行8。
- 重启失败，请联系技术支持工程师处理。
12. 等待3分钟，查看告警是否自动清除。
- 是，处理完成。
- 否，执行13。
13. 请联系技术支持工程师处理。
参考信息
无。