# 5.2.11.1.41 0x1000C90033 登录FTP服务器被拒绝

##### 告警解释
用户（[User]）登录FTP服务器（路径：[Ftp_path]）被拒绝。
##### 告警属性
| 告警ID | 告警级别 | 手工清除 |
| --- | --- | --- |
| 0x1000C90033 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| User | FTP用户。 |
| Ftp_path | FTP服务器路径。 |
##### 对系统的影响
管理数据备份到FTP服务器失败。
##### 可能原因
- 登录FTP服务器的用户名或密码不正确。
- FTP服务器要求客户端必须采用FTPS协议进行通信。
##### 处理步骤
- 可能原因1：登录FTP服务器的用户名或密码不正确。
- 联系管理员获取正确的FTP服务器的用户名和密码。
- 使用浏览器，依次登录Manager和Server的GUI。
登录地址：https://Workflow-Management-Float-IP字段对应的IP地址或datamover_management_float_ip字段对应的IP地址:8088
默认帐号：admin，默认密码：使用HUAWEI CLOUD Stack Deploy安装为*****，手工方式安装为*****。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，进入“设置备份存储”界面，检查配置的FTP服务器用户和密码是否正确。
- 是，执行2。
- 否，执行1.e。
- 重新配置FTP备份存储。详细请参考FTP章节。
- 可能原因2：FTP服务器要求客户端必须采用FTPS协议进行通信。
- 选择“设置 > 配置 > 管理数据备份”，进入“管理数据备份”界面。
- 单击“设置备份存储”，进入“设置备份存储”界面，检查配置的协议类型是否为FTPS。
- 是，请联系技术支持工程师协助解决。
- 否，执行2.c。
- 修改FTP协议配置为FTPS，重新配置可用的FTP备份存储，详细请参考FTP章节。
##### 参考信息
无