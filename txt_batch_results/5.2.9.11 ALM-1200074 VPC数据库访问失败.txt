# ******** ALM-1200074 VPC数据库访问失败

##### 告警解释
系统每隔60秒检查一次VPC Service访问VPC数据库的连接状态，如果持续不通，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 1200074 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 告警名称 | 此条告警信息的名称。 |
| 来源系统 | 告警来源。 |
| IP地址 | 产生告警信息的云服务器的IP地址。 |
| 最近发生时间 | 此条告警信息最近发生时间。 |
| 定位信息 | 产生告警的定位信息。 |
| 附加信息 | 告警附加信息。 |
##### 对系统的影响
VPC Service节点无法访问VPC DB，将导致虚拟私有云功能不可用，需尽快处理。
##### 可能原因
- VPC Service节点的网络不通。
- VPC DB数据库服务异常。
##### 处理步骤
1. 使用浏览器，登录ManageOne运维面。
- 登录地址：https://ManageOne运维面主页的访问地址:31943。例如，https://oc.type.com:31943。
- 默认帐号：admin，默认密码：*****。
2. 单击“登录”。
3. 在页面上方的菜单栏，选择“集中告警”。
4. 在告警列表中，找到待处理的告警记录，单击待处理告警行左侧的，进入“告警详情”页。
5. 在“告警详情”页的“定位信息”中，“主机IP”即告警节点IP地址。
当出现多条相同告警时，告警信息被汇聚。此时在“被汇聚告警”页签查看告警节点IP地址。
6. 使用“PuTTY”，登录告警节点。
默认帐号： vpc，默认密码： *****
7. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
8. 执行下面命令，获取数据库地址。
cat /home/<USER>/network/WEB-INF/classes/config/extension.properties |grep network.db.url
回显如下所示，记录数据库地址 pub-db.regionid.network.com。
network.db.url=jdbc\:postgresql\://pub-db.regionid.network.com:5432/vpc?use_boolean\=true
9. 执行以下命令，获取数据库节点的浮动IP。
ping 数据库地址
其中，数据库地址为8中记录的地址。
例如：
ping pub-db.regionid.network.com
回显如下所示，记录数据库节点的浮动IP **************。
[vpc@PUB-SRV01 config]$ ping pub-db.regionid.network.com
PING pub-db.regionid.network.com (**************) 56(84) bytes of data.
64 bytes from ************** (**************): icmp_seq=1 ttl=64 time=0.193 ms
64 bytes from ************** (**************): icmp_seq=2 ttl=64 time=0.271 ms
10. 使用“PuTTY”，登录PUB-DB-01或PUB-DB-02（VPC DB节点）。
默认帐号： vpc，默认密码： *****
11. 执行以下命令，防止“PuTTY”超时退出。
TMOUT=0
12. 执行ifconfig命令，查看浮动IP地址是否和获取到的地址一致。
回显如下所示：
eth0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500
inet ***************  netmask *************  broadcast ***************
inet6 fe80::f816:3eff:feab:d1b9  prefixlen 64  scopeid 0x20<link>
ether fa:16:3e:ab:d1:b9  txqueuelen 1000  (Ethernet)
RX packets 61250645  bytes 20429432458 (19.0 GiB)
RX errors 0  dropped 0  overruns 0  frame 0
TX packets 53692510  bytes 27388569156 (25.5 GiB)
TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
eth0:1: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500
inet **************  netmask *************  broadcast ***************
ether fa:16:3e:ab:d1:b9  txqueuelen 1000  (Ethernet)
lo: flags=73<UP,LOOPBACK,RUNNING>  mtu 65536
inet 127.0.0.1  netmask *********
inet6 ::1  prefixlen 128  scopeid 0x10<host>
loop  txqueuelen 1000  (Local Loopback)
RX packets 8209772  bytes 4629798489 (4.3 GiB)
RX errors 0  dropped 0  overruns 0  frame 0
TX packets 8209772  bytes 4629798489 (4.3 GiB)
TX errors 0  dropped 0 overruns 0  carrier 0  collisions 0
lo:1: flags=73<UP,LOOPBACK,RUNNING>  mtu 65536
inet **************  netmask 0.0.0.0
loop  txqueuelen 1000  (Local Loopback)
- 如果IP地址一致，请执行13检查DB服务状态。
- 如果IP地址不一致，请联系技术支持工程师协助解决。
13. 执行以下命令，检查DB服务状态。
sudo service had query
根据提示输入root用户密码后，正常回显如下：
[root@PUB-DB01 vpc]# sudo service had query
NODE                     ROLE           PHASE         RESS            VER             START
PUB-DB01(PUB-DB01)      active         Actived       normal          V100R001C01     2019-06-13 19:36:31
PUB-DB02(PUB-DB02)      standby        Deactived     normal          V100R001C01     2019-06-13 19:37:36
--------------------------------------------------------------------------------------------------------
ID    RES                     STAT             RET             TYPE
PUB-DB01(PUB-DB01):       1     exfloatip               Normal           Normal          Single_active
2     gaussDB                 Normal           Active_normal   Active_standby
PUB-DB02(PUB-DB02):       1     exfloatip               Normal           Stopping        Single_active
2     gaussDB                 Normal           Standby_normal  Active_standby
- 如果RESS列为normal，表示服务状态正常。
- 如果RESS列不是normal，表示服务状态异常，请联系技术支持工程师协助解决。
14. 等待2分钟后检查告警是否自动清除。
##### 告警清除
此告警修复后，系统会自动清除此告警，无需手工清除。
##### 参考信息
无。