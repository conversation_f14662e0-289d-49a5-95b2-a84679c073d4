# 5.2.3.1.77 ALM-73205 Iaas层资源配置不一致告警

##### 告警解释
用户在进行Iaas层的资源隔离或者角色配置后，如果主机节点上的资源配置不生效，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 73205 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 主机名：产生告警的主机名称。 |
| 附加信息 | 第一个参数：产生告警的主机ID。<br>第二个参数：产生告警的主机名。<br>第三个参数：资源配置不一样故障详情。 |
##### 对系统的影响
主机上资源配置和生效不一致。
##### 可能原因
主机上存在野虚拟机。
##### 处理步骤
1. 根据当前环境部署的场景，获取对应的“手动审计”章节，重新触发系统审计。
- Region Type I：
- 判断部署的场景是级联层还是被级联层的方法：在OpenStack首节点，执行命令cps productinfo-show，查看product_type的取值，cascading表示级联层，cascaded表示被级联层。
- 如果审计类告警出现在被级联层，则无论级联层是否同时出现告警，都应当先处理被级联层告警，待告警恢复后，再次执行级联层的审计（可手动触发审计，或等待级联层每日自动进行的审计），以确认级联层与被级联层之间的信息同步。
- 级联层：手动审计
- KVM虚拟化（被级联层）：手动审计
- Region Type II&Region Type III：
- FusionCompute虚拟化：手动审计
- KVM虚拟化：手动审计
2. 参考“收集审计报告”章节，获取野虚拟机审计报告。
从审计报告中获取uuid、hyper_vm_name、host_id。
- Region Type I：
- 级联层：收集审计报告
- KVM虚拟化（被级联层）：收集审计报告
- Region Type II&Region Type III：
- FusionCompute虚拟化：收集审计报告
- KVM虚拟化：收集审计报告
3. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
4. 选择“云化服务 > FusionSphere Openstack OM”，单击FusionSphere Openstack OM页面链接，打开FusionSphere Openstack OM的管理控制台。
5. 登录Service OM界面。
6. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，查看“Iaas层资源配置不一致告警”的告警信息，获取告警对象host_id。
7. 参考“处理野虚拟机”章节，清理系统中的野虚拟机。
- Region Type I场景：
- 级联层：处理野虚拟机
- KVM虚拟化（被级联层）：处理野虚拟机
- Region Type II&Region Type III场景：
- FusionCompute虚拟化：处理野虚拟机
- KVM虚拟化：处理野虚拟机
8. 清理完野虚拟机之后，执行以下命令。
cps commit
9. 参考3~5登录FusionSphere Openstack OM控制台。
10. 选择“监控 > 告警 > 告警列表 > OpenStack告警”，查看“Iaas层资源配置不一致告警”的告警是否被清除。
- 是，告警处理结束。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。