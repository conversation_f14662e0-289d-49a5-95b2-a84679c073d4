# 5.2.3.1.39 ALM-70126 异构VMware野虚拟机残留告警

##### 告警解释
异常场景导致vCenter上存在虚拟机，而openstack数据库中的记录已经被删除，这个时候会触发该告警（该告警目前只针对异构对接VMware场景的虚拟机）。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70126 | 重要 | 否 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID |
| 附加信息 | 主机ID：告警虚拟机所在主机ID<br>虚拟机在vmware上的名称：告警虚拟机在vmware上的名称 |
##### 对系统的影响
野虚拟机会影响对用户的数据展示，可能会影响相关数据的统计。
##### 可能原因
- 使用管理数据备份恢复功能，把数据库回退到之前的一个备份点。在该备份点之后，有创建虚拟机的操作，但使用上个备份点的数据恢复管理数据，可能会使这段时间的数据库记录被回退，而vCenter上没有变化，导致出现野虚拟机。
- 直接在vCenter上创建虚拟机。
##### 处理步骤
1. 查看告警信息，可获得虚拟机id(instance_id)，虚拟机在vmware上的名称。
2. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
3. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
4. 执行以下命令，防止系统超时退出。
TMOUT=0
5. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
6. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
7. 执行：nova show instance_id，查看该虚拟机是否存在。
- 是，非野虚拟机，手动清除告警。
- 否，执行8。
8. 登录vCenter根据虚拟机在vmware上的名称查看该虚拟机是否存在。
- 是，确认为野虚拟机，执行9。
- 否，执行10。
9. 确认可以删除虚拟机后，删除vCenter上该虚拟机，并查看vCenter上是否删除成功。
- 是，手动清除告警。
- 否，执行10。
10. 请联系技术支持工程师协助解决。
##### 参考信息
无。