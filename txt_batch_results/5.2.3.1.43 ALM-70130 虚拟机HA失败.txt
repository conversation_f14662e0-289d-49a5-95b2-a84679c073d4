# 5.2.3.1.43 ALM-70130 虚拟机HA失败

##### 告警解释
当虚拟机HA失败时，系统产生此告警。
- 后续当虚拟机恢复到稳态（已运行或者已停止）时，该告警会自动消除。
- 如果环境已无空闲资源，或者由于虚拟机亲和性设置导致无主机执行HA时，此告警无法自动消除，需先恢复环境后，手动/后续自动清除此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 70130 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 虚拟机ID：产生告警的虚拟机ID<br>租户ID：告警虚拟机所在租户ID |
| 附加信息 | 可用分区名：告警虚拟机所在可用分区名称<br>虚拟机名：告警虚拟机的名称<br>主机名：告警虚拟机所在主机名称<br>主机ID：告警虚拟机所在主机ID |
##### 对系统的影响
标示FusionSphere系统已触发虚拟机HA，但是HA恢复虚拟机失败，后续FusionSphere系统将尝试在本地/异地重新恢复虚拟机。
##### 可能原因
- 环境已无空闲资源，导致虚拟机HA时无可用主机。
- 虚拟机存在亲和性设置，导致虚拟机HA时无法选择到主机。
##### 处理步骤
1. 查看该告警是否已经自动清除。
该告警一般会在虚拟机HA恢复后自动清除。
- 是，结束处理。
- 否，长时间未清除，可能是系统当前无可用主机来执行HA，转到2。
2. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
3. 在Service OM界面，选择“资源 > 计算资源 > 主机”。
4. 查看虚拟机所在主机是否为下电状态或者Service OM上是否有主机类告警。
- 是，先排除故障，尝试拉起主机，待10分钟后观察虚拟机是否已恢复?
- 主机无法恢复，建议参考更换主机及配件相关章节。
- 主机已恢复，因亲和性规则，虚拟机无法自动HA恢复的场景，需要手动指定合适的主机做冷迁移，转到5。
- 虚拟机已恢复，结束处理。
- 否，转到6。
5. 进入“虚拟机”页面，单击虚拟机名称，进入虚拟机“概要”页面，在右上角选择“更多 > 冷迁移”，弹出“警告”对话框，单击“确认”。
指定主机进行迁移可能会打破主机组亲和性规则，请确认选择符合条件的目标主机。
6. 如果告警仍然不能清除，请联系技术支持工程师协助解决。
##### 参考信息
无。