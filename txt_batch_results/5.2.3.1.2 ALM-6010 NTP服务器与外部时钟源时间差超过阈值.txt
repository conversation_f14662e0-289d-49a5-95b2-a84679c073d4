# 5.2.3.1.2 ALM-6010 NTP服务器与外部时钟源时间差超过阈值

##### 告警解释
在配置外部时钟源之后，ntp-server（NTP服务器）会周期性（默认为2min）检查主ntp-server所在节点与外部时钟源的时间差（如果存在多个外部时钟源，则逐一检查），当任意一个时间差超过阈值时，产生此告警。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 6010 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：产生告警的云服务。<br>Region：产生告警的region名。<br>Object：产生告警的服务。 |
| 附加信息 | 主机ID：产生告警的主机ID。<br>主机名：产生告警的主机名。<br>本端地址：NTP客户端ip。<br>对端地址：外部时钟源ip。<br>阈值：ntp-server和外部时钟源间可缓慢同步的最大时差。 |
##### 对系统的影响
如果本地主ntp-server所在节点与外部时钟源时间差超过阈值，则不与外部时钟源进行时间同步。
##### 可能原因
- 本地主ntp-server所在节点时间被修改。
- 外部时钟源时间发生跳变。
- 第一次跟外部时钟源对接时，用户没有触发强制同步操作。
- 配置了多个外部时钟源，ntp-server和某个外部时钟源时差超过阈值。
##### 处理步骤
1. 登录FusionSphere OpenStack安装部署界面。
具体请参见登录FusionSphere OpenStack安装部署界面（ManageOne方式）章节。
2. 选择“运维”，进入“日常维护”。
3. 稍等2分钟，在“时间同步”标签下，提示由“系统时间状态查询中”变为“系统时间异常”，如下图所示。请根据界面提示查看详情，获取上报告警主机所对应的时间差。
4. 选择“配置”，进入“系统”。打开“NTP”标签，查看已配置的外部时钟源信息，如下图所示。
5. 使用PuTTY，通过External OM平面IP地址登录OpenStack首节点。
默认帐号：fsp，默认密码：*****。
系统同时支持密码和公私钥对身份进行认证，如果使用公私钥对进行登录认证请参见使用PuTTY通过公私钥对认证方式登录节点。
External OM平面IP地址请参考软件安装阶段HUAWEI CLOUD Stack Deploy导出的参数信息汇总文件《xxx_export_all.xlsm》的“工具生成的IP参数”页签搜索IP参数名称获取。不同场景参数名称如下所示：
- Region Type I场景级联层：Cascading-ExternalOM-Reverse-Proxy，被级联层：Cascaded-ExternalOM-Reverse-Proxy。
- Region Type II和Type III场景：ExternalOM-Reverse-Proxy。
6. 执行以下命令，并按提示输入“root”用户的密码，切换至“root”用户。
su - root
默认密码：“*****”。
7. 执行以下命令，防止系统超时退出。
TMOUT=0
8. 执行以下命令，导入环境变量。
source set_env
回显如下类似信息：
please choose environment variable which you want to import:
(1) openstack environment variable (keystone v3)
(2) cps environment variable
(3) openstack environment variable legacy (keystone v2)
(4) openstack environment variable of cloud_admin (keystone v3)
please choose:[1|2|3|4]
9. 输入“1”，选择使用keystone v3鉴权，按照提示输入OS_USERNAME对应帐号的密码。
默认密码为：*****。
10. 执行如下命令，获取ntp-server部署情况，包含节点id信息，回显结果中runsonhost属性的值。
cps template-instance-list --service ntp ntp-server
回显如下类似信息：
+------------+----------------+--------+------------+----------------+
| instanceid | componenttype  | status |runsonhost  |omip            |
+------------+----------------+--------+------------+----------------+
| 1          | ntp-server     | active | Mgmt1      |***********     |
| 0          | ntp-server     | standby| Mgmt3      |************    |
+------------+----------------+--------+------------+----------------+
回显信息数目取决于ntp-server的部署方式，status显示active字段的即为主NTP Server所在的节点。
11. 根据获取到的IP信息，登录主ntp-server部署所在节点，用户名与密码参照5～6。
12. 执行以下命令，确认是否和每台服务器之间的系统时间差都小于阈值。
ntpdate -d 外部时钟源IP
42174775-2DA8-B93F-CF47-47902E7AA2B0:~ # ntpdate -d **************
24 Jul 19:11:52 ntpdate[5469]: ntpdate 4.2.6p5@1.2349-o Thu Dec 20 00:00:00 UTC 2018 (1)
Looking for host ************** and service ntp
host found : **************
transmit(**************)
receive(**************)
transmit(**************)
receive(**************)
transmit(**************)
receive(**************)
transmit(**************)
receive(**************)
server **************, port 123
stratum 11, precision -24, leap 00, trust 000
refid [**************], delay 0.02592, dispersion 0.00005
transmitted 4, in filter 4
reference time:    e0e2bb54.e30f606d  Wed, Jul 24 2019 19:11:16.886
originate timestamp: e0e2bb7f.e6f1a121  Wed, Jul 24 2019 19:11:59.902
transmit timestamp:  e0e2bb7f.0a6cb785  Wed, Jul 24 2019 19:11:59.040
filter delay:  0.02679  0.02612  0.02629  0.02592
0.00000  0.00000  0.00000  0.00000
filter offset: 0.861150 0.860937 0.860968 0.861003
0.000000 0.000000 0.000000 0.000000
delay 0.02592, dispersion 0.00005
offset 0.861003
24 Jul 19:11:59 ntpdate[5469]: step time server ************** offset 0.861003 sec
- 是，请联系技术支持工程师协助解决。
- 否，执行13。
13. 检查是否存在不准确的时钟源。
- 是，移除不准确的外部时钟源服务器配置，并执行14。
- 否，请联系技术支持工程师协助解决。
14. 再次执行3，查看提示是否由“系统时间状态查询中”变为“当前系统时间正常”，如图所示。
- 是，执行15。
- 否，请联系技术支持工程师协助解决。
15. 等待5分钟～10分钟，查看告警是否清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。
##### 参考信息
无。