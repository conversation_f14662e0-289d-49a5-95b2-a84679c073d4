# *******.24 ALM-9917 Service OM虚拟机CPU占用率超过阈值

##### 告警解释
Service OM组件周期（默认为5分钟）检查环境上自身虚拟机CPU使用率情况。如果CPU使用率超过80%，则产生此告警。如果使用率低于该阈值，告警恢复。
##### 告警属性
| 告警ID | 告警级别 | 可自动清除 |
| --- | --- | --- |
| 9917 | 重要 | 是 |
##### 告警参数
| 参数名称 | 参数含义 |
| --- | --- |
| 定位信息 | 云服务：固定为ServiceOM<br>IP地址：ServiceOM虚拟机的固定IP地址 |
| 附加信息 | 云服务：固定为ServiceOM<br>阈值：告警上报阈值<br>使用率：CPU使用率 |
##### 对系统的影响
Service OM所在虚拟机CPU利用率超过阈值，可能导致Service OM所在虚拟机响应缓慢，命令超时，或出现无法正常运行等现象。
##### 可能原因
某些进程未被及时处理占用太多的CPU资源。
##### 处理步骤
1. 登录Service OM界面。
具体操作请参见登录和注销Service OM界面。
2. 选择“ 系统 > 任务与日志 > 任务中心”，观察是否有正在执行的任务。
- 是，请等待任务执行结束之后，执行3。
- 否，执行4。
3. 等待10分钟左右，检查告警是否自动清除。
- 是，处理完毕。
- 否，执行4。
4. 选择“系统 > 任务与日志 > 操作日志”，请查看是否有当前5分钟内的操作日志。
- 是，执行5。
- 否，执行6。
5. 等待10分钟左右，检查告警是否自动清除。
- 是，处理完毕。
- 否，执行6。
6. 等待超过30分钟，检查告警是否自动清除。
- 是，处理完毕。
- 否，请联系技术支持工程师协助解决。