#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import csv
from pathlib import Path
from collections import defaultdict

def extract_chapter_number(filename):
    """
    从文件名中提取章节号
    例如：5.2.6.2.3.1.2 -> 5.2.6.2.3.1.2
    """
    # 匹配开头的章节号格式：数字.数字.数字...
    match = re.match(r'^(\d+(?:\.\d+)*)', filename)
    if match:
        return match.group(1)
    return None

def get_parent_chapters(chapter_number):
    """
    获取章节号的所有父章节号
    例如：5.2.6.2.3.1.2 -> [5, 5.2, 5.2.6, 5.2.6.2, 5.2.6.2.3, 5.2.6.2.3.1]
    """
    if not chapter_number:
        return []
    
    parts = chapter_number.split('.')
    parents = []
    
    for i in range(1, len(parts)):
        parent = '.'.join(parts[:i])
        parents.append(parent)
    
    return parents

def extract_keywords_from_filename(filename):
    """
    从文件名中提取关键词
    """
    keywords = []
    
    # 移除文件扩展名
    name_without_ext = filename.replace('.txt', '')
    
    # 1. 提取章节号
    chapter_number = extract_chapter_number(name_without_ext)
    if chapter_number:
        keywords.append(chapter_number)
    
    # 2. 提取ALM关键词
    if 'ALM' in name_without_ext:
        keywords.append('ALM')
    
    # 3. 提取告警ID
    # 匹配各种告警ID格式
    alert_id_patterns = [
        r'ALM-([A-Za-z0-9_\-]+)',  # ALM-xxx格式
        r'(0x[A-Fa-f0-9]+)',       # 0x开头的十六进制
        r'ALM-(\d+)',              # ALM-数字
        r'(\d{6,})',               # 6位以上纯数字
    ]
    
    for pattern in alert_id_patterns:
        matches = re.findall(pattern, name_without_ext)
        for match in matches:
            if match not in keywords:
                keywords.append(match)
    
    # 4. 提取告警标题部分
    # 先移除章节号
    remaining = name_without_ext
    if chapter_number:
        remaining = remaining[len(chapter_number):].strip()
    
    # 移除告警ID部分
    for pattern in alert_id_patterns:
        remaining = re.sub(pattern, '', remaining).strip()
    
    # 移除ALM前缀
    remaining = re.sub(r'^ALM[-\s]*', '', remaining).strip()
    
    # 清理多余的符号和空格
    remaining = re.sub(r'^[-\s]+', '', remaining).strip()
    remaining = re.sub(r'[-\s]+$', '', remaining).strip()
    
    # 如果还有内容，作为标题关键词
    if remaining:
        keywords.append(remaining)
    
    return keywords

def build_chapter_file_mapping(txt_dir):
    """
    构建章节号到文件名的映射
    """
    chapter_to_file = {}
    txt_path = Path(txt_dir)
    
    for txt_file in txt_path.glob("*.txt"):
        filename = txt_file.name
        chapter_number = extract_chapter_number(filename)
        if chapter_number:
            chapter_to_file[chapter_number] = filename
    
    return chapter_to_file

def extract_all_keywords(txt_dir, output_csv):
    """
    为所有文件提取关键词并生成CSV
    """
    txt_path = Path(txt_dir)
    
    # 构建章节号到文件名的映射
    chapter_to_file = build_chapter_file_mapping(txt_dir)
    
    # 存储每个文件的关键词
    file_keywords = {}
    
    # 第一步：为每个文件提取基本关键词
    for txt_file in sorted(txt_path.glob("*.txt")):
        filename = txt_file.name
        keywords = extract_keywords_from_filename(filename)
        file_keywords[filename] = set(keywords)  # 使用set避免重复
    
    # 第二步：添加父章节的关键词
    for filename in file_keywords:
        chapter_number = extract_chapter_number(filename)
        if chapter_number:
            parent_chapters = get_parent_chapters(chapter_number)
            
            for parent_chapter in parent_chapters:
                if parent_chapter in chapter_to_file:
                    parent_filename = chapter_to_file[parent_chapter]
                    if parent_filename in file_keywords:
                        # 添加父章节的关键词
                        parent_keywords = file_keywords[parent_filename]
                        file_keywords[filename].update(parent_keywords)
    
    # 生成统计信息
    total_files = len(file_keywords)
    files_with_keywords = sum(1 for keywords in file_keywords.values() if keywords)
    
    print(f"处理完成:")
    print(f"总文件数: {total_files}")
    print(f"有关键词的文件数: {files_with_keywords}")
    
    # 写入CSV文件
    with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['document_name', 'keywords']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        
        for filename in sorted(file_keywords.keys()):
            keywords_list = sorted(list(file_keywords[filename]))  # 转换为排序的列表
            keywords_str = ';'.join(keywords_list) if keywords_list else ''
            
            writer.writerow({
                'document_name': filename,
                'keywords': keywords_str
            })
    
    print(f"CSV文件已保存到: {output_csv}")
    
    # 显示一些示例
    print(f"\n关键词提取示例:")
    count = 0
    for filename in sorted(file_keywords.keys()):
        if file_keywords[filename] and count < 10:
            keywords_str = ';'.join(sorted(list(file_keywords[filename])))
            print(f"  {filename}")
            print(f"    关键词: {keywords_str}")
            count += 1

if __name__ == "__main__":
    txt_directory = "txt_batch_results"
    output_csv_file = "document_keywords.csv"
    
    print("开始提取文档关键词...")
    print(f"源目录: {txt_directory}")
    print(f"输出CSV: {output_csv_file}")
    print("-" * 50)
    
    extract_all_keywords(txt_directory, output_csv_file)
    
    print("-" * 50)
    print("关键词提取完成！")
