#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import csv
from pathlib import Path

def extract_alert_id_from_content(file_path):
    """
    从文档内容中提取告警ID
    在告警属性表格中查找告警ID
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找告警属性表格中的告警ID
        # 匹配模式：| 告警ID | ... |
        # 下一行：| --- | --- | --- |
        # 再下一行：| 实际ID | ... |
        
        # 方法1：查找表格中的告警ID
        table_pattern = r'\|\s*告警ID\s*\|[^\n]*\n\s*\|[^|]*\|[^|]*\|[^|]*\|\s*\n\s*\|\s*([^|]+?)\s*\|'
        match = re.search(table_pattern, content, re.MULTILINE)
        
        if match:
            alert_id = match.group(1).strip()
            return alert_id
        
        # 方法2：如果方法1失败，尝试更简单的模式
        # 查找 "| 告警ID |" 后面几行内的ID
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '告警ID' in line and '|' in line:
                # 查找后续几行中的ID
                for j in range(i+1, min(i+5, len(lines))):
                    next_line = lines[j].strip()
                    if next_line.startswith('|') and not next_line.startswith('| ---'):
                        # 提取第一个单元格的内容
                        parts = next_line.split('|')
                        if len(parts) >= 2:
                            potential_id = parts[1].strip()
                            if potential_id and potential_id != '告警ID':
                                return potential_id
        
        return ""
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return ""

def extract_alert_title_from_filename(filename, alert_id):
    """
    从文件名中提取告警标题
    如果存在告警ID，提取ID后的描述部分
    """
    if not alert_id:
        return ""
    
    try:
        # 移除文件扩展名
        name_without_ext = filename.replace('.txt', '')
        
        # 查找告警ID在文件名中的位置
        # 支持多种格式：ALM-xxx, 0x开头, 纯数字等
        
        # 尝试不同的ID格式匹配
        patterns = [
            rf'ALM-{re.escape(alert_id)}',  # ALM-48320
            rf'{re.escape(alert_id)}',      # 直接匹配ID
        ]
        
        for pattern in patterns:
            match = re.search(pattern, name_without_ext, re.IGNORECASE)
            if match:
                # 找到ID后，提取后面的部分作为标题
                start_pos = match.end()
                remaining = name_without_ext[start_pos:].strip()
                
                # 清理标题：移除开头的连字符、空格等
                title = re.sub(r'^[-\s]+', '', remaining).strip()
                
                # 如果标题不为空，返回
                if title:
                    return title
        
        # 如果上述方法都失败，尝试从章节号后提取
        # 匹配模式：5.x.x.x.x 后面的内容
        chapter_pattern = r'^\d+(?:\.\d+)*\s+(.+)$'
        match = re.match(chapter_pattern, name_without_ext)
        if match:
            full_title = match.group(1)
            # 如果包含告警ID，提取ID后的部分
            if alert_id in full_title:
                id_pos = full_title.find(alert_id)
                if id_pos != -1:
                    title = full_title[id_pos + len(alert_id):].strip()
                    title = re.sub(r'^[-\s]+', '', title).strip()
                    if title:
                        return title
            return full_title
        
        return ""
        
    except Exception as e:
        print(f"处理文件名 {filename} 时出错: {e}")
        return ""

def process_all_files(txt_dir, output_csv):
    """
    处理所有TXT文件并生成CSV
    """
    txt_path = Path(txt_dir)
    results = []
    
    processed_count = 0
    found_id_count = 0
    found_title_count = 0
    
    print("开始处理文件...")
    
    for txt_file in sorted(txt_path.glob("*.txt")):
        processed_count += 1
        filename = txt_file.name
        
        print(f"处理文件 {processed_count}: {filename}")
        
        # 提取告警ID
        alert_id = extract_alert_id_from_content(txt_file)
        if alert_id:
            found_id_count += 1
            print(f"  找到告警ID: {alert_id}")
        
        # 提取告警标题
        alert_title = extract_alert_title_from_filename(filename, alert_id)
        if alert_title:
            found_title_count += 1
            print(f"  提取标题: {alert_title}")
        
        results.append({
            'document_name': filename,
            'alert_id': alert_id,
            'alert_title': alert_title
        })
    
    # 写入CSV文件
    with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['document_name', 'alert_id', 'alert_title']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for result in results:
            writer.writerow(result)
    
    print(f"\n处理完成:")
    print(f"总文件数: {processed_count}")
    print(f"找到告警ID的文件数: {found_id_count}")
    print(f"提取到标题的文件数: {found_title_count}")
    print(f"CSV文件已保存到: {output_csv}")

if __name__ == "__main__":
    txt_directory = "txt_batch_results"
    output_csv_file = "alert_labels.csv"
    
    print("开始提取告警标签...")
    print(f"源目录: {txt_directory}")
    print(f"输出CSV: {output_csv_file}")
    print("-" * 50)
    
    process_all_files(txt_directory, output_csv_file)
    
    print("-" * 50)
    print("告警标签提取完成！")
