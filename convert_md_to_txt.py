#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
from pathlib import Path

def convert_md_to_txt(source_dir, target_dir):
    """
    将Markdown文件转换为TXT格式
    保持内容不变，只是改变文件扩展名
    """
    source_path = Path(source_dir)
    target_path = Path(target_dir)
    
    # 确保目标目录存在
    target_path.mkdir(exist_ok=True)
    
    converted_count = 0
    total_files = 0
    
    for md_file in source_path.glob("*.md"):
        total_files += 1
        print(f"转换文件: {md_file.name}")
        
        try:
            # 读取Markdown文件内容
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 生成对应的TXT文件名
            txt_filename = md_file.stem + '.txt'  # 将.md替换为.txt
            target_file = target_path / txt_filename
            
            # 写入TXT文件
            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            converted_count += 1
            print(f"  ✓ 转换完成: {txt_filename}")
            
        except Exception as e:
            print(f"  ✗ 转换失败: {md_file.name} - {e}")
    
    print(f"\n转换完成:")
    print(f"总文件数: {total_files}")
    print(f"成功转换: {converted_count}")
    print(f"失败数量: {total_files - converted_count}")

if __name__ == "__main__":
    source_directory = "cleaned_batch_results"
    target_directory = "txt_batch_results"
    
    print("开始将Markdown文件转换为TXT格式...")
    print(f"源目录: {source_directory}")
    print(f"目标目录: {target_directory}")
    print("-" * 50)
    
    convert_md_to_txt(source_directory, target_directory)
    
    print("-" * 50)
    print("Markdown到TXT转换完成！")
