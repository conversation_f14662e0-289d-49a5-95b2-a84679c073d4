document_name,keywords
5.2.10 弹性负载均衡.txt,5.2.10;弹性负载均衡
5.2.10.1 ALM-1223005 数据库连接异常.txt,1223005;5.2.10.1;ALM;弹性负载均衡;数据库连接异常
5.2.10.2 ALM-1223006 ETCD集群健康检查告警.txt,1223006;5.2.10.2;ALM;ETCD集群健康检查告警;弹性负载均衡
5.2.10.3 ALM-1223013 集群中存在主机连接异常.txt,1223013;5.2.10.3;ALM;弹性负载均衡;集群中存在主机连接异常
5.2.10.4 ALM-1223014 僵尸进程告警.txt,1223014;5.2.10.4;ALM;僵尸进程告警;弹性负载均衡
5.2.10.5 ALM-1223016 ELB管理节点脑裂告警.txt,1223016;5.2.10.5;ALM;ELB管理节点脑裂告警;弹性负载均衡
5.2.11 灾备服务.txt,5.2.11;灾备服务
******** eBackup.txt,********;eBackup;灾备服务
********.1 0x1000F40000 License文件无效.txt,0x1000F40000;********.1;License文件无效;eBackup;灾备服务
********.10 0x201000F40008 License授权容量即将耗尽.txt,0x201000F40008;201000;********.10;License授权容量即将耗尽;eBackup;灾备服务
********.11 0x201000F4000C 存在License不支持的特性.txt,0x201000F4000C;201000;********.11;eBackup;存在License不支持的特性;灾备服务
********.12 0x201000F40013 存在License不支持的特性.txt,0x201000F40013;201000;********.12;eBackup;存在License不支持的特性;灾备服务
********.13 0x201000F40014 存在License不支持的特性.txt,0x201000F40014;201000;********.13;eBackup;存在License不支持的特性;灾备服务
********.14 0x201000F40016 存在License不支持的特性.txt,0x201000F40016;201000;********.14;eBackup;存在License不支持的特性;灾备服务
********.15 0x201000F40017 存在License不支持的特性.txt,0x201000F40017;201000;********.15;eBackup;存在License不支持的特性;灾备服务
********.16 0x201000F40018 存在License不支持的特性.txt,0x201000F40018;201000;********.16;eBackup;存在License不支持的特性;灾备服务
********.17 0x********** 事件转储目录所用空间已超出阈值.txt,0x**********;**********;********.17;eBackup;事件转储目录所用空间已超出阈值;灾备服务
********.18 0x20100031000A 证书校验失败.txt,0x20100031000A;20100031000;********.18;eBackup;灾备服务;证书校验失败
********.19 0x20100031000C 证书校验失败.txt,0x20100031000C;20100031000;********.19;eBackup;灾备服务;证书校验失败
********.2 0x1000F40001 License未配置.txt,0x1000F40001;********.2;License未配置;eBackup;灾备服务
********.20 0x201000310010 访问告警服务器失败.txt,0x201000310010;201000310010;********.20;eBackup;灾备服务;访问告警服务器失败
********.21 0x201000310015 数据库连接失败.txt,0x201000310015;201000310015;********.21;eBackup;数据库连接失败;灾备服务
********.22 0x210000000101 微服务注册失败.txt,0x210000000101;210000000101;********.22;eBackup;微服务注册失败;灾备服务
********.23 0x210000000100 微服务已停止.txt,0x210000000100;210000000100;********.23;eBackup;微服务已停止;灾备服务
********.24 0x********** 证书已经过期.txt,0x**********;********.24;**********;eBackup;灾备服务;证书已经过期
********.25 0x********** 证书即将过期.txt,0x**********;********.25;**********;eBackup;灾备服务;证书即将过期
********.26 0x210000000200 证书校验失败.txt,0x210000000200;210000000200;********.26;eBackup;灾备服务;证书校验失败
********.27 0x105800860001 清理备份记录失败.txt,0x105800860001;105800860001;********.27;eBackup;清理备份记录失败;灾备服务
********.28 0x21000000090E 清理leftover删除快照失败.txt,0x21000000090E;21000000090;********.28;eBackup;清理leftover删除快照失败;灾备服务
********.29 0x21000000090F 组件连接异常.txt,0x21000000090F;21000000090;********.29;eBackup;灾备服务;组件连接异常
********.3 0x1000F40002 License进入宽限期.txt,0x1000F40002;********.3;License进入宽限期;eBackup;灾备服务
********.30 0x210000000901 监控进程启动失败.txt,0x210000000901;210000000901;********.30;eBackup;灾备服务;监控进程启动失败
********.31 0x2010E01D0005 证书校验失败.txt,0x2010E01D0005;********.31;eBackup;灾备服务;证书校验失败
********.32 0x1010E01A0018 执行备份时CBT机制未生效.txt,0x1010E01A0018;********.32;eBackup;执行备份时CBT机制未生效;灾备服务
********.33 0x101000C90003 系统配置数据所占空间已超出最大阈值.txt,0x101000C90003;101000;********.33;eBackup;灾备服务;系统配置数据所占空间已超出最大阈值
********.34 0x101000C90004 系统配置数据所占空间已超出阈值.txt,0x101000C90004;101000;********.34;eBackup;灾备服务;系统配置数据所占空间已超出阈值
********.35 0x1000C90002 访问系统数据库备份共享存储失败.txt,0x1000C90002;********.35;eBackup;灾备服务;访问系统数据库备份共享存储失败
********.36 0x1000C90035 证书校验失败.txt,0x1000C90035;********.36;eBackup;灾备服务;证书校验失败
********.37 0x1000C90003 系统数据库备份共享存储空间不足.txt,0x1000C90003;********.37;eBackup;灾备服务;系统数据库备份共享存储空间不足
********.38 0x1000C90005 未配置备份服务器.txt,0x1000C90005;********.38;eBackup;未配置备份服务器;灾备服务
********.39 0x1000C90006 证书校验失败.txt,0x1000C90006;********.39;eBackup;灾备服务;证书校验失败
********.4 0x1000F40003 License已经过期.txt,0x1000F40003;********.4;License已经过期;eBackup;灾备服务
********.40 0x1000C90032 FTP服务器空间不足.txt,0x1000C90032;********.40;FTP服务器空间不足;eBackup;灾备服务
********.41 0x1000C90033 登录FTP服务器被拒绝.txt,0x1000C90033;********.41;eBackup;灾备服务;登录FTP服务器被拒绝
********.42 0x1000C90034 上传管理数据到FTP服务器失败.txt,0x1000C90034;********.42;eBackup;上传管理数据到FTP服务器失败;灾备服务
********.43 0x1000C90004 当前挂载的系统数据库备份共享存储类型与预置类型不匹配.txt,0x1000C90004;********.43;eBackup;当前挂载的系统数据库备份共享存储类型与预置类型不匹配;灾备服务
********.44 0x2010E00E0007 eBackup没有存储单元的写权限.txt,0x2010E00E0007;********.44;eBackup;eBackup没有存储单元的写权限;灾备服务
********.45 0x2010E00E000A 证书校验失败.txt,0x2010E00E000A;********.45;eBackup;灾备服务;证书校验失败
********.46 0x2010E00E0006 存储单元没有可用容量.txt,0x2010E00E0006;********.46;eBackup;存储单元没有可用容量;灾备服务
********.47 0x2010E01D0006 证书校验失败.txt,0x2010E01D0006;********.47;eBackup;灾备服务;证书校验失败
********.48 0x1000C90000 eBackup服务器之间失去连接.txt,0x1000C90000;********.48;eBackup;eBackup服务器之间失去连接;灾备服务
********.49 0x10E00C0000 存储库容量不足.txt,0x10E00C0000;********.49;eBackup;存储库容量不足;灾备服务
********.5 0x101000F40000 License ESN不匹配.txt,0x101000F40000;101000;********.5;License ESN不匹配;eBackup;灾备服务
********.50 0x10E01C0000 添加Workflow或Proxy失败.txt,0x10E01C0000;********.50;eBackup;添加Workflow或Proxy失败;灾备服务
********.51 0x********** HA的证书已过期.txt,0x**********;********.51;**********;HA的证书已过期;eBackup;灾备服务
********.52 0x10E01C0027 服务进程异常.txt,0x10E01C0027;********.52;eBackup;服务进程异常;灾备服务
********.53 0x10E00E0000 连接存储单元失败.txt,0x10E00E0000;********.53;eBackup;灾备服务;连接存储单元失败
********.54 0x10E01C0029 浮动IP连接异常.txt,0x10E01C0029;********.54;eBackup;浮动IP连接异常;灾备服务
********.55 0x10E01C0001 Workflow（Proxy）和Manager（Server）的版本不兼容.txt,0x10E01C0001;********.55;Workflow（Proxy）和Manager（Server）的版本不兼容;eBackup;灾备服务
********.56 0x10E01C0002 主备参数不一致导致HA功能异常.txt,0x10E01C0002;********.56;eBackup;主备参数不一致导致HA功能异常;灾备服务
********.57 0x10E01C0003 恢复主备倒换功能失败.txt,0x10E01C0003;********.57;eBackup;恢复主备倒换功能失败;灾备服务
********.58 0x10E01C0004 HA主备节点心跳中断.txt,0x10E01C0004;********.58;HA主备节点心跳中断;eBackup;灾备服务
********.59 0x10E01C0005 HA主节点向备节点同步文件失败.txt,0x10E01C0005;********.59;HA主节点向备节点同步文件失败;eBackup;灾备服务
********.6 0x101000F40001 License文件版本不匹配.txt,0x101000F40001;101000;********.6;License文件版本不匹配;eBackup;灾备服务
********.60 0x10E01C0009 HA仲裁网关不可达.txt,0x10E01C0009;********.60;HA仲裁网关不可达;eBackup;灾备服务
********.61 0x10E01C000B 数据库升主失败.txt,0x10E01C000B;********.61;eBackup;数据库升主失败;灾备服务
********.62 0x10E01C000E AdminNode服务异常.txt,0x10E01C000E;********.62;AdminNode服务异常;eBackup;灾备服务
********.63 0x10E01C000F 浮动IP服务异常.txt,0x10E01C000F;********.63;eBackup;浮动IP服务异常;灾备服务
********.64 0x10E01C0010 GaussDB服务异常.txt,0x10E01C0010;********.64;GaussDB服务异常;eBackup;灾备服务
********.65 0x10E01C0011 ibase服务异常.txt,0x10E01C0011;********.65;eBackup;ibase服务异常;灾备服务
********.66 0x10E01C0028 证书校验失败.txt,0x10E01C0028;********.66;eBackup;灾备服务;证书校验失败
********.67 0x10E00E0001 访问存储单元失败.txt,0x10E00E0001;********.67;eBackup;灾备服务;访问存储单元失败
********.68 0x10E0140001 扫描受保护环境失败.txt,0140001;0x10E0140001;********.68;eBackup;扫描受保护环境失败;灾备服务
********.69 0x10E0140000 连接受保护环境失败.txt,0140000;0x10E0140000;********.69;eBackup;灾备服务;连接受保护环境失败
********.7 0x201000F40004 试用即将到期.txt,0x201000F40004;201000;********.7;eBackup;灾备服务;试用即将到期
********.70 0x2010E014000C 证书校验失败.txt,014000;0x2010E014000C;********.70;eBackup;灾备服务;证书校验失败
********.71 0x10E00D0000 存储池空间使用率超出临界值.txt,0x10E00D0000;********.71;eBackup;存储池空间使用率超出临界值;灾备服务
********.72 0x201000C90002 不支持当前时区信息.txt,0x201000C90002;201000;********.72;eBackup;不支持当前时区信息;灾备服务
********.73 0x201000C90025 NTP时间差异过大.txt,0x201000C90025;201000;********.73;NTP时间差异过大;eBackup;灾备服务
********.74 0x201000C90024 eBackup服务器到NTP服务器连接异常.txt,0x201000C90024;201000;********.74;eBackup;eBackup服务器到NTP服务器连接异常;灾备服务
********.75 0x201000C90009 eBackup服务器未设置NTP时钟源.txt,0x201000C90009;201000;********.75;eBackup;eBackup服务器未设置NTP时钟源;灾备服务
********.76 0x5800790001 SFTP服务器空间不足.txt,0x5800790001;********.76;5800790001;SFTP服务器空间不足;eBackup;灾备服务
********.77 0x5800790002 登录SFTP服务器被拒绝.txt,0x5800790002;********.77;5800790002;eBackup;灾备服务;登录SFTP服务器被拒绝
********.78 0x5800790003 上传管理数据到SFTP服务器失败.txt,0x5800790003;********.78;5800790003;eBackup;上传管理数据到SFTP服务器失败;灾备服务
********.79 0x10E01A0010 备份副本中缺失受保护对象元数据.txt,0x10E01A0010;********.79;eBackup;备份副本中缺失受保护对象元数据;灾备服务
********.8 0x201000F40005 试用期已过.txt,0x201000F40005;201000;********.8;eBackup;灾备服务;试用期已过
********.80 0x10E01A0011 备份副本的元数据损坏.txt,0x10E01A0011;********.80;eBackup;备份副本的元数据损坏;灾备服务
********.81 0x10E01A0019 删除备份副本失败.txt,0x10E01A0019;********.81;eBackup;删除备份副本失败;灾备服务
********.82 0x2010E01A0008 检测到备份副本的数据块有损坏.txt,0x2010E01A0008;********.82;eBackup;检测到备份副本的数据块有损坏;灾备服务
********.83 0x2010E01A000E 验证备份副本的任务失败.txt,0x2010E01A000E;********.83;eBackup;灾备服务;验证备份副本的任务失败
********.84 0x201000C9000A Proxy上的NTP服务异常.txt,0x201000C9000A;201000;********.84;Proxy上的NTP服务异常;eBackup;灾备服务
********.85 0x210000000D00 连接远端vpp服务器失败.txt,0x210000000D00;210000000;********.85;eBackup;灾备服务;连接远端vpp服务器失败
********.86 0x201000C90021 清理复制任务的残留资源失败.txt,0x201000C90021;201000;********.86;eBackup;清理复制任务的残留资源失败;灾备服务
********.87 0x2010E01A001D 复制任务失败.txt,0x2010E01A001D;********.87;eBackup;复制任务失败;灾备服务
********.88 0x10E01A0014 卸载FusionStorage卷失败.txt,0x10E01A0014;********.88;eBackup;卸载FusionStorage卷失败;灾备服务
********.89 0x10E01A0015 删除FusionStorage卷失败.txt,0x10E01A0015;********.89;eBackup;删除FusionStorage卷失败;灾备服务
********.9 0x201000F40007 License授权容量耗尽.txt,0x201000F40007;201000;********.9;License授权容量耗尽;eBackup;灾备服务
********.90 0x10E01A0017 卸载OceanStor V3V5卷或者Dorado V3卷失败.txt,0x10E01A0017;********.90;eBackup;卸载OceanStor V3V5卷或者Dorado V3卷失败;灾备服务
********.91 0x********** 重删数据有冗余.txt,0x**********;********.91;**********;eBackup;灾备服务;重删数据有冗余
********.92 0x********** 访问ManageOne失败.txt,0x**********;********.92;**********;eBackup;灾备服务;访问ManageOne失败
********.93 0x105800740001 备份代理存在进度长时间未更新任务.txt,0x105800740001;105800740001;********.93;eBackup;备份代理存在进度长时间未更新任务;灾备服务
********.94 附录.txt,********.94;eBackup;灾备服务;附录
********.94.1 登录eBackup服务器.txt,********.94.1;eBackup;灾备服务;登录eBackup服务器;附录
5.2.11.2 Karbor.txt,5.2.11.2;Karbor;灾备服务
5.2.11.2.1 1020799 创建云服务器复制副本失败.txt,1020799;5.2.11.2.1;Karbor;创建云服务器复制副本失败;灾备服务
5.2.11.2.10 1020771 云硬盘备份策略自动调度失败.txt,1020771;5.2.11.2.10;Karbor;云硬盘备份策略自动调度失败;灾备服务
5.2.11.2.11 1020770 云硬盘复制策略自动调度失败.txt,1020770;5.2.11.2.11;Karbor;云硬盘复制策略自动调度失败;灾备服务
5.2.11.2.12 1020768 云硬盘备份失败.txt,1020768;5.2.11.2.12;Karbor;云硬盘备份失败;灾备服务
5.2.11.2.13 1020762 FSP证书校验失败.txt,1020762;5.2.11.2.13;FSP证书校验失败;Karbor;灾备服务
5.2.11.2.14 1020761 IAM证书校验失败.txt,1020761;5.2.11.2.14;IAM证书校验失败;Karbor;灾备服务
5.2.11.2.15 1023299 节点状态异常.txt,1023299;5.2.11.2.15;Karbor;灾备服务;节点状态异常
5.2.11.2.16 1023298 组件状态异常.txt,1023298;5.2.11.2.16;Karbor;灾备服务;组件状态异常
5.2.11.2.17 1023296 外部NTP时钟同步异常.txt,1023296;5.2.11.2.17;Karbor;外部NTP时钟同步异常;灾备服务
5.2.11.2.18 1023295 备份系统数据发生失败.txt,1023295;5.2.11.2.18;Karbor;备份系统数据发生失败;灾备服务
5.2.11.2.19 1023282 FTP服务器证书校验失败.txt,1023282;5.2.11.2.19;FTP服务器证书校验失败;Karbor;灾备服务
5.2.11.2.2 1020796 云服务器备份配额消耗到配额总量的阈值.txt,1020796;5.2.11.2.2;Karbor;云服务器备份配额消耗到配额总量的阈值;灾备服务
5.2.11.2.20 1023279 系统证书即将过期.txt,1023279;5.2.11.2.20;Karbor;灾备服务;系统证书即将过期
5.2.11.2.21 1023278 系统证书已经过期.txt,1023278;5.2.11.2.21;Karbor;灾备服务;系统证书已经过期
5.2.11.2.22 1023277 消息队列卡死.txt,1023277;5.2.11.2.22;Karbor;消息队列卡死;灾备服务
5.2.11.2.23 1023276 消息队列产生网络分区.txt,1023276;5.2.11.2.23;Karbor;消息队列产生网络分区;灾备服务
5.2.11.2.24 1023099 CPU使用率超过阈值.txt,1023099;5.2.11.2.24;CPU使用率超过阈值;Karbor;灾备服务
5.2.11.2.25 1023098 内存使用率超过阈值.txt,1023098;5.2.11.2.25;Karbor;内存使用率超过阈值;灾备服务
5.2.11.2.26 1023097 磁盘使用率超过阈值.txt,1023097;5.2.11.2.26;Karbor;灾备服务;磁盘使用率超过阈值
5.2.11.2.27 1020800 执行复制策略失败.txt,1020800;5.2.11.2.27;Karbor;执行复制策略失败;灾备服务
5.2.11.2.28 1020803 跨区域复制策略自动调度失败.txt,1020803;5.2.11.2.28;Karbor;灾备服务;跨区域复制策略自动调度失败
5.2.11.2.29 1020759 连接ManageOne运营平台失败.txt,1020759;5.2.11.2.29;Karbor;灾备服务;连接ManageOne运营平台失败
5.2.11.2.3 1020791 云服务器备份策略自动调度失败.txt,1020791;5.2.11.2.3;Karbor;云服务器备份策略自动调度失败;灾备服务
5.2.11.2.30 1020758 上报计量数据失败.txt,1020758;5.2.11.2.30;Karbor;上报计量数据失败;灾备服务
5.2.11.2.31 1023093 备份服务节点间网络异常.txt,1023093;5.2.11.2.31;Karbor;备份服务节点间网络异常;灾备服务
5.2.11.2.32 1020756 注册CSBS-VBS到统一证书管理服务失败.txt,1020756;5.2.11.2.32;Karbor;注册CSBS-VBS到统一证书管理服务失败;灾备服务
5.2.11.2.4 1020790 云服务器备份复制策略自动调度失败.txt,1020790;5.2.11.2.4;Karbor;云服务器备份复制策略自动调度失败;灾备服务
5.2.11.2.5 1020788 云服务器备份失败.txt,1020788;5.2.11.2.5;Karbor;云服务器备份失败;灾备服务
5.2.11.2.6 1020783 启动消息队列服务失败.txt,1020783;5.2.11.2.6;Karbor;启动消息队列服务失败;灾备服务
5.2.11.2.7 1020782 消息队列存在消息响应超时.txt,1020782;5.2.11.2.7;Karbor;消息队列存在消息响应超时;灾备服务
5.2.11.2.8 1020779 创建云硬盘复制副本失败.txt,1020779;5.2.11.2.8;Karbor;创建云硬盘复制副本失败;灾备服务
5.2.11.2.9 1020776 云硬盘备份配额消耗到配额总量的阈值.txt,1020776;5.2.11.2.9;Karbor;云硬盘备份配额消耗到配额总量的阈值;灾备服务
5.2.11.3 eReplication.txt,5.2.11.3;eReplication;灾备服务
5.2.11.3.1 0x3230014 备份失败.txt,0x3230014;3230014;5.2.11.3.1;eReplication;备份失败;灾备服务
5.2.11.3.10 0x3230034 HA网关不通.txt,0x3230034;3230034;5.2.11.3.10;HA网关不通;eReplication;灾备服务
5.2.11.3.11 0x3230036 仲裁服务异常.txt,0x3230036;3230036;5.2.11.3.11;eReplication;仲裁服务异常;灾备服务
5.2.11.3.12 0x3230037 服务实例故障恢复失败.txt,0x3230037;3230037;5.2.11.3.12;eReplication;服务实例故障恢复失败;灾备服务
5.2.11.3.13 0x3230038 服务实例重保护失败.txt,0x3230038;3230038;5.2.11.3.13;eReplication;服务实例重保护失败;灾备服务
5.2.11.3.14 0x323003A 虚拟机不满足保护要求.txt,0x323003A;323003;5.2.11.3.14;eReplication;灾备服务;虚拟机不满足保护要求
5.2.11.3.15 0x323003B 虚拟机中已卸载的卷未从服务实例中清理.txt,0x323003B;323003;5.2.11.3.15;eReplication;灾备服务;虚拟机中已卸载的卷未从服务实例中清理
5.2.11.3.16 0x323003C 虚拟机的卷未创建容灾保护.txt,0x323003C;323003;5.2.11.3.16;eReplication;灾备服务;虚拟机的卷未创建容灾保护
5.2.11.3.17 0x323003D 受保护虚拟机从服务实例中被移除.txt,0x323003D;323003;5.2.11.3.17;eReplication;受保护虚拟机从服务实例中被移除;灾备服务
5.2.11.3.18 0x323003E 服务实例不满足故障恢复要求.txt,0x323003E;323003;5.2.11.3.18;eReplication;服务实例不满足故障恢复要求;灾备服务
5.2.11.3.19 0x323003F IAM证书更新失败.txt,0x323003F;323003;5.2.11.3.19;IAM证书更新失败;eReplication;灾备服务
5.2.11.3.2 0x3230024 证书已过期.txt,0x3230024;3230024;5.2.11.3.2;eReplication;灾备服务;证书已过期
5.2.11.3.20 0x3230041 上报计量信息失败.txt,0x3230041;3230041;5.2.11.3.20;eReplication;上报计量信息失败;灾备服务
5.2.11.3.21 0x3230042 连接日志服务器异常.txt,0x3230042;3230042;5.2.11.3.21;eReplication;灾备服务;连接日志服务器异常
5.2.11.3.22 0x3230043 高可用集群中高斯数据库主备间复制中断.txt,0x3230043;3230043;5.2.11.3.22;eReplication;灾备服务;高可用集群中高斯数据库主备间复制中断
5.2.11.3.23 0x3230046 共享卷关联的虚拟机不在同一个服务实例中.txt,0x3230046;3230046;5.2.11.3.23;eReplication;共享卷关联的虚拟机不在同一个服务实例中;灾备服务
5.2.11.3.24 0x3230047 订单实施结果通知失败.txt,0x3230047;3230047;5.2.11.3.24;eReplication;灾备服务;订单实施结果通知失败
5.2.11.3.25 0x3230048 License授权容量耗尽.txt,0x3230048;3230048;5.2.11.3.25;License授权容量耗尽;eReplication;灾备服务
5.2.11.3.26 0x3230049 系统90天试用期到期.txt,0x3230049;3230049;5.2.11.3.26;eReplication;灾备服务;系统90天试用期到期
5.2.11.3.27 0x323005C 证书即将过期.txt,0x323005C;323005;5.2.11.3.27;eReplication;灾备服务;证书即将过期
5.2.11.3.28 0x323005D 证书已经过期.txt,0x323005D;323005;5.2.11.3.28;eReplication;灾备服务;证书已经过期
5.2.11.3.29 0x3230064 对接管理平台失败.txt,0x3230064;3230064;5.2.11.3.29;eReplication;对接管理平台失败;灾备服务
5.2.11.3.3 0x3230025 证书校验失败.txt,0x3230025;3230025;5.2.11.3.3;eReplication;灾备服务;证书校验失败
5.2.11.3.4 0x323002C 占位虚拟机不存在.txt,0x323002C;323002;5.2.11.3.4;eReplication;占位虚拟机不存在;灾备服务
5.2.11.3.5 0x323002D 占位虚拟机未配置.txt,0x323002D;323002;5.2.11.3.5;eReplication;占位虚拟机未配置;灾备服务
5.2.11.3.6 0x323002F 一致性组状态异常.txt,0x323002F;323002;5.2.11.3.6;eReplication;一致性组状态异常;灾备服务
5.2.11.3.7 0x3230030 HA心跳中断.txt,0x3230030;3230030;5.2.11.3.7;HA心跳中断;eReplication;灾备服务
5.2.11.3.8 0x3230031 HA同步失败.txt,0x3230031;3230031;5.2.11.3.8;HA同步失败;eReplication;灾备服务
5.2.11.3.9 0x3230033 HA链路中断.txt,0x3230033;3230033;5.2.11.3.9;HA链路中断;eReplication;灾备服务
5.2.15 消息通知服务.txt,5.2.15;消息通知服务
5.2.15.1 ALM-2000401 tomcat进程不存在.txt,2000401;5.2.15.1;ALM;tomcat进程不存在;消息通知服务
5.2.15.10 ALM-2000463 GaussdbHA上传远端备份服务器失败.txt,2000463;5.2.15.10;ALM;GaussdbHA上传远端备份服务器失败;消息通知服务
5.2.15.11 ALM-2000471 zookeeper进程不存在.txt,2000471;5.2.15.11;ALM;zookeeper进程不存在;消息通知服务
5.2.15.12 ALM-2000493 证书异常.txt,2000493;5.2.15.12;ALM;消息通知服务;证书异常
5.2.15.2 ALM-2000420 ns进程不存在.txt,2000420;5.2.15.2;ALM;ns进程不存在;消息通知服务
5.2.15.3 ALM-2000421 ps进程不存在.txt,2000421;5.2.15.3;ALM;ps进程不存在;消息通知服务
5.2.15.4 ALM-2000456 memcached进程不存在.txt,2000456;5.2.15.4;ALM;memcached进程不存在;消息通知服务
5.2.15.5 ALM-2000469 kafka进程不存在.txt,2000469;5.2.15.5;ALM;kafka进程不存在;消息通知服务
5.2.15.6 ALM-2000488 smn_haproxy进程不存在.txt,2000488;5.2.15.6;ALM;smn_haproxy进程不存在;消息通知服务
5.2.15.7 ALM-2000489 smn_keepalived进程不存在.txt,2000489;5.2.15.7;ALM;smn_keepalived进程不存在;消息通知服务
5.2.15.8 ALM-2000460 GaussdbHA服务进程异常.txt,2000460;5.2.15.8;ALM;GaussdbHA服务进程异常;消息通知服务
5.2.15.9 ALM-2000462 GaussdbHA备份失败.txt,2000462;5.2.15.9;ALM;GaussdbHA备份失败;消息通知服务
5.2.17 公共组件.txt,5.2.17;公共组件
5.2.17.1 ECS UI.txt,5.2.17.1;ECS UI;公共组件
5.2.17.1.1 ALM-1160001 ECS_UI Tomcat进程不存在.txt,1160001;5.2.17.1.1;ALM;ECS UI;ECS_UI Tomcat进程不存在;公共组件
5.2.17.1.2 ALM-1160003 ECS_UI中ntp进程故障.txt,1160003;5.2.17.1.2;ALM;ECS UI;ECS_UI中ntp进程故障;公共组件
5.2.17.10 SDR.txt,5.2.17.10;SDR;公共组件
5.2.17.10.1 计量话单告警参考.txt,5.2.17.10.1;SDR;公共组件;计量话单告警参考
5.2.17.10.1.1 ALM-2000301 计量话单生成话单失败.txt,2000301;5.2.17.10.1.1;ALM;SDR;公共组件;计量话单告警参考;计量话单生成话单失败
5.2.17.10.1.2 ALM-2000317 计量话单服务异常.txt,2000317;5.2.17.10.1.2;ALM;SDR;公共组件;计量话单告警参考;计量话单服务异常
5.2.17.10.1.3 ALM-2000327 计量话单证书告警.txt,2000327;5.2.17.10.1.3;ALM;SDR;公共组件;计量话单告警参考;计量话单证书告警
5.2.17.10.1.4 ALM-2000328 计量话单证书告警.txt,2000328;5.2.17.10.1.4;ALM;SDR;公共组件;计量话单告警参考;计量话单证书告警
5.2.17.10.2 参考信息.txt,5.2.17.10.2;SDR;公共组件;参考信息
5.2.17.10.2.1 配置屏蔽规则.txt,5.2.17.10.2.1;SDR;公共组件;参考信息;配置屏蔽规则
5.2.17.11 CCS.txt,5.2.17.11;CCS;公共组件
5.2.17.11.1 ALM-1320004 ccs进程异常.txt,1320004;5.2.17.11.1;ALM;CCS;ccs进程异常;公共组件
5.2.17.11.2 ALM-1320019 证书异常.txt,1320019;5.2.17.11.2;ALM;CCS;公共组件;证书异常
5.2.17.12 云平台仲裁服务.txt,5.2.17.12;云平台仲裁服务;公共组件
5.2.17.12.1 ALM-2000266 节点系统时钟跳变超过一分钟.txt,2000266;5.2.17.12.1;ALM;云平台仲裁服务;公共组件;节点系统时钟跳变超过一分钟
5.2.17.12.2 ALM-2001106 Etcd服务状态异常.txt,2001106;5.2.17.12.2;ALM;Etcd服务状态异常;云平台仲裁服务;公共组件
5.2.17.12.3 ALM-2002101 Monitor进程异常.txt,2002101;5.2.17.12.3;ALM;Monitor进程异常;云平台仲裁服务;公共组件
5.2.17.12.4 ALM-2002302 Monitor与对端站点通信异常.txt,2002302;5.2.17.12.4;ALM;Monitor与对端站点通信异常;云平台仲裁服务;公共组件
5.2.17.12.5 ALM-2002501 站点网络状态异常.txt,2002501;5.2.17.12.5;ALM;云平台仲裁服务;公共组件;站点网络状态异常
5.2.17.12.6 ALM-2001107 第三方站点异常.txt,2001107;5.2.17.12.6;ALM;云平台仲裁服务;公共组件;第三方站点异常
5.2.17.2 DMK.txt,5.2.17.2;DMK;公共组件
5.2.17.2.1 ALM-8000888 dmk服务异常.txt,5.2.17.2.1;8000888;ALM;DMK;dmk服务异常;公共组件
5.2.17.3 GaussDB.txt,5.2.17.3;GaussDB;公共组件
5.2.17.3.1 ALM-1510000 GaussdbHA服务进程异常.txt,1510000;5.2.17.3.1;ALM;GaussDB;GaussdbHA服务进程异常;公共组件
5.2.17.3.2 ALM-1510002 GaussdbHA备份失败.txt,1510002;5.2.17.3.2;ALM;GaussDB;GaussdbHA备份失败;公共组件
5.2.17.3.3 ALM-1510003 GaussdbHA证书异常.txt,1510003;5.2.17.3.3;ALM;GaussDB;GaussdbHA证书异常;公共组件
5.2.17.3.4 ALM-1510005 GaussdbHA主备同步异常.txt,1510005;5.2.17.3.4;ALM;GaussDB;GaussdbHA主备同步异常;公共组件
5.2.17.3.5 ALM-1510006 GaussdbHA上传远端备份服务器失败.txt,1510006;5.2.17.3.5;ALM;GaussDB;GaussdbHA上传远端备份服务器失败;公共组件
5.2.17.4 LVS.txt,5.2.17.4;LVS;公共组件
5.2.17.4.1 ALM-2001101 lvs_keepalived进程不存在.txt,2001101;5.2.17.4.1;ALM;LVS;lvs_keepalived进程不存在;公共组件
5.2.17.5 Nginx.txt,5.2.17.5;Nginx;公共组件
5.2.17.5.1 ALM-2001002 nginx服务异常.txt,2001002;5.2.17.5.1;ALM;Nginx;nginx服务异常;公共组件
5.2.17.5.2 ALM-2001006 证书异常.txt,2001006;5.2.17.5.2;ALM;Nginx;公共组件;证书异常
5.2.17.6 DNS.txt,5.2.17.6;DNS;公共组件
5.2.17.6.1 ALM-8000021 DNS named进程异常.txt,5.2.17.6.1;8000021;ALM;DNS;DNS named进程异常;公共组件
******** NTP.txt,********;NTP;公共组件
********.1 ALM-7700073 NTP时钟源偏移告警.txt,********.1;7700073;ALM;NTP;NTP时钟源偏移告警;公共组件
********.2 ALM-7700071 NTP进程异常.txt,********.2;7700071;ALM;NTP;NTP进程异常;公共组件
******** HAProxy.txt,********;HAProxy;公共组件
********.1 ALM-2000904 haproxy服务异常.txt,2000904;********.1;ALM;HAProxy;haproxy服务异常;公共组件
********.2 ALM-2000906 haproxy的浮动IP不可达.txt,2000906;********.2;ALM;HAProxy;haproxy的浮动IP不可达;公共组件
********.3 ALM-2000908 keepalived进程不存在.txt,2000908;********.3;ALM;HAProxy;keepalived进程不存在;公共组件
********.4 ALM-2000909 haproxy浮动IP端口不可达.txt,2000909;********.4;ALM;HAProxy;haproxy浮动IP端口不可达;公共组件
******** TaskCenter.txt,********;TaskCenter;公共组件
********.1 ALM-2000722 taskcenter服务异常.txt,2000722;********.1;ALM;TaskCenter;taskcenter服务异常;公共组件
********.2 ALM-2000724 证书异常.txt,2000724;********.2;ALM;TaskCenter;公共组件;证书异常
5.2.19 APIGateway.txt,5.2.19;APIGateway
******** 通信告警.txt,********;APIGateway;通信告警
********.1 ALM-48101-无法访问shubao.txt,48101;48101-;********.1;ALM;APIGateway;无法访问shubao;通信告警
********.10 ALM-48118-无法访问redis-mgr.txt,48118;48118-;********.10;ALM;APIGateway;无法访问redis-mgr;通信告警
********.2 ALM-48102-无法访问etcd.txt,48102;48102-;********.2;ALM;APIGateway;无法访问etcd;通信告警
********.3 ALM-48103-无法访问orchestration.txt,48103;48103-;********.3;ALM;APIGateway;无法访问orchestration;通信告警
********.4 ALM-48104-无法访问authadv.txt,48104;48104-;********.4;ALM;APIGateway;无法访问authadv;通信告警
********.5 ALM-48107-无法访问FTP.txt,48107;48107-;********.5;ALM;APIGateway;无法访问FTP;通信告警
********.6 ALM-48108-无法访问shubao-orchestration.txt,48108;48108-;********.6;ALM;APIGateway;无法访问shubao-orchestration;通信告警
********.7 ALM-48110-无法访问DB.txt,48110;48110-;********.7;ALM;APIGateway;无法访问DB;通信告警
********.8 ALM-48111-无法访问apimgr.txt,48111;48111-;********.8;ALM;APIGateway;无法访问apimgr;通信告警
********.9 ALM-48117-无法正常接收opsagent心跳.txt,48117;48117-;********.9;ALM;APIGateway;无法正常接收opsagent心跳;通信告警
5.2.19.2 处理错误告警.txt,5.2.19.2;APIGateway;处理错误告警
5.2.19.2.1 ALM-48303-无法读写FTP文件.txt,48303;48303-;5.2.19.2.1;ALM;APIGateway;处理错误告警;无法读写FTP文件
5.2.19.2.2 ALM-48304-证书即将过期.txt,48304;48304-;5.2.19.2.2;ALM;APIGateway;处理错误告警;证书即将过期
5.2.19.2.3 ALM-48305-证书已经过期.txt,48305;48305-;5.2.19.2.3;ALM;APIGateway;处理错误告警;证书已经过期
5.2.19.2.4 ALM-48306-证书校验失败.txt,48306;48306-;5.2.19.2.4;ALM;APIGateway;处理错误告警;证书校验失败
5.2.19.2.5 ALM-48316-请求超出单实例流控阈值.txt,48316;48316-;5.2.19.2.5;ALM;APIGateway;处理错误告警;请求超出单实例流控阈值
5.2.19.2.6 ALM-48317-重新加载LB失败.txt,48317;48317-;5.2.19.2.6;ALM;APIGateway;处理错误告警;重新加载LB失败
5.2.19.2.7 ALM-48318-证书回退失败.txt,48318;48318-;5.2.19.2.7;ALM;APIGateway;处理错误告警;证书回退失败
5.2.19.2.8 ALM-48319-证书无法生效.txt,48319;48319-;5.2.19.2.8;ALM;APIGateway;处理错误告警;证书无法生效
5.2.19.2.9 ALM-48320-证书即将过期.txt,48320;48320-;5.2.19.2.9;ALM;APIGateway;处理错误告警;证书即将过期
5.2.19.3 设备告警.txt,5.2.19.3;APIGateway;设备告警
5.2.19.3.1 ALM-48401-进程未启动.txt,48401;48401-;5.2.19.3.1;ALM;APIGateway;设备告警;进程未启动
5.2.19.3.2 ALM-48402-端口冲突.txt,48402;48402-;5.2.19.3.2;ALM;APIGateway;端口冲突;设备告警
5.2.19.3.3 ALM-48409-文件句柄使用率超过门限值.txt,48409;48409-;5.2.19.3.3;ALM;APIGateway;文件句柄使用率超过门限值;设备告警
******* FusionSphere OpenStack告警参考.txt,*******;FusionSphere OpenStack告警参考
*******.1 ALM-6008 上传日志到OBS服务失败.txt,*******.1;6008;ALM;FusionSphere OpenStack告警参考;上传日志到OBS服务失败
*******.10 ALM-6021 主机网口状态异常.txt,*******.10;6021;ALM;FusionSphere OpenStack告警参考;主机网口状态异常
*******.100 ALM-1126002 裸金属服务器审计告警.txt,1126002;*******.100;ALM;FusionSphere OpenStack告警参考;裸金属服务器审计告警
*******.101 ALM-1200067 DHCP服务不可用.txt,1200067;*******.101;ALM;DHCP服务不可用;FusionSphere OpenStack告警参考
*******.102 ALM-1200075 主机设备端口错包告警.txt,1200075;*******.102;ALM;FusionSphere OpenStack告警参考;主机设备端口错包告警
*******.103 ALM-1200076 主机设备端口丢包告警.txt,1200076;*******.103;ALM;FusionSphere OpenStack告警参考;主机设备端口丢包告警
*******.104 ALM-1200077 Dpdk lacp bond聚合失败.txt,1200077;*******.104;ALM;Dpdk lacp bond聚合失败;FusionSphere OpenStack告警参考
*******.105 ALM-1223017 负载均衡器后端实例不在线.txt,1223017;*******.105;ALM;FusionSphere OpenStack告警参考;负载均衡器后端实例不在线
*******.106 ALM-1240001 FusionSphere Neutron和Agile Controller-DCN 进行数据一致性修复时失败.txt,1240001;*******.106;ALM;FusionSphere Neutron和Agile Controller-DCN 进行数据一致性修复时失败;FusionSphere OpenStack告警参考
*******.107 ALM-1240002 FusionSphere Neutron和Agile Controller-DCN HTTP连接故障.txt,1240002;*******.107;ALM;FusionSphere Neutron和Agile Controller-DCN HTTP连接故障;FusionSphere OpenStack告警参考
*******.108 ALM-1240003 FusionSphere Neutron和Agile Controller-DCN Websocket连接故障.txt,1240003;*******.108;ALM;FusionSphere Neutron和Agile Controller-DCN Websocket连接故障;FusionSphere OpenStack告警参考
*******.109 ALM-1301021 EVSOVS-DPDK 转发能力达到瓶颈告警.txt,1301021;*******.109;ALM;EVSOVS-DPDK 转发能力达到瓶颈告警;FusionSphere OpenStack告警参考
*******.11 ALM-6022 主机与NTP服务器心跳状态异常.txt,*******.11;6022;ALM;FusionSphere OpenStack告警参考;主机与NTP服务器心跳状态异常
*******.110 ALM-1316000 OpenStack服务包升级完成后未提交.txt,1316000;*******.110;ALM;FusionSphere OpenStack告警参考;OpenStack服务包升级完成后未提交
*******.111 ALM-1316001 忽略升级的单板未处理.txt,1316001;*******.111;ALM;FusionSphere OpenStack告警参考;忽略升级的单板未处理
*******.112 ALM-1316002 热补丁运行异常告警.txt,1316002;*******.112;ALM;FusionSphere OpenStack告警参考;热补丁运行异常告警
*******.113 ALM-1316003 QEMU版本异常告警.txt,1316003;*******.113;ALM;FusionSphere OpenStack告警参考;QEMU版本异常告警
*******.114 ALM-1507002 RabbitMQ资源使用率超过阈值.txt,1507002;*******.114;ALM;FusionSphere OpenStack告警参考;RabbitMQ资源使用率超过阈值
*******.12 ALM-6023 主机存储链路中断.txt,*******.12;6023;ALM;FusionSphere OpenStack告警参考;主机存储链路中断
*******.13 ALM-6024 存储资源管理链路中断或认证失败.txt,*******.13;6024;ALM;FusionSphere OpenStack告警参考;存储资源管理链路中断或认证失败
*******.14 ALM-6025 存储使用率超过阈值.txt,*******.14;6025;ALM;FusionSphere OpenStack告警参考;存储使用率超过阈值
*******.15 ALM-6026 主机光纤通道中断.txt,*******.15;6026;ALM;FusionSphere OpenStack告警参考;主机光纤通道中断
*******.16 ALM-6027 网口自协商速率没有达到服务器网口最大速率的一半.txt,*******.16;6027;ALM;FusionSphere OpenStack告警参考;网口自协商速率没有达到服务器网口最大速率的一半
*******.17 ALM-6028 本地NTP客户端与本地NTP服务器时间差超过60秒.txt,*******.17;6028;ALM;FusionSphere OpenStack告警参考;本地NTP客户端与本地NTP服务器时间差超过60秒
*******.18 ALM-6029 服务自动备份失败.txt,*******.18;6029;ALM;FusionSphere OpenStack告警参考;服务自动备份失败
*******.19 ALM-6030 IP冲突故障.txt,*******.19;6030;ALM;FusionSphere OpenStack告警参考;IP冲突故障
*******.2 ALM-6010 NTP服务器与外部时钟源时间差超过阈值.txt,*******.2;6010;ALM;FusionSphere OpenStack告警参考;NTP服务器与外部时钟源时间差超过阈值
*******.20 ALM-6031 上传日志到FTP服务器失败.txt,*******.20;6031;ALM;FusionSphere OpenStack告警参考;上传日志到FTP服务器失败
*******.21 ALM-6033 I层服务CPU占用率超过阈值.txt,*******.21;6033;ALM;FusionSphere OpenStack告警参考;I层服务CPU占用率超过阈值
*******.22 ALM-6034 I层服务内存占用率超过阈值.txt,*******.22;6034;ALM;FusionSphere OpenStack告警参考;I层服务内存占用率超过阈值
*******.23 ALM-6036 虚拟机CPU占用率超过阈值.txt,*******.23;6036;ALM;FusionSphere OpenStack告警参考;虚拟机CPU占用率超过阈值
*******.24 ALM-6037 虚拟机虚拟内存Swap分区占用率超过阈值.txt,*******.24;6037;ALM;FusionSphere OpenStack告警参考;虚拟机虚拟内存Swap分区占用率超过阈值
*******.25 ALM-6038 主机磁盘占用率超过阈值.txt,*******.25;6038;ALM;FusionSphere OpenStack告警参考;主机磁盘占用率超过阈值
*******.26 ALM-6039 主机虚拟化域内存占用率超过阈值.txt,*******.26;6039;ALM;FusionSphere OpenStack告警参考;主机虚拟化域内存占用率超过阈值
*******.27 ALM-70100 虚拟机审计告警.txt,*******.27;70100;ALM;FusionSphere OpenStack告警参考;虚拟机审计告警
*******.28 ALM-70101 虚拟机操作系统故障告警.txt,*******.28;70101;ALM;FusionSphere OpenStack告警参考;虚拟机操作系统故障告警
*******.29 ALM-70102 虚拟机ERROR状态告警.txt,*******.29;70102;ALM;FusionSphere OpenStack告警参考;虚拟机ERROR状态告警
*******.3 ALM-6014 DNS服务器连接中断.txt,*******.3;6014;ALM;DNS服务器连接中断;FusionSphere OpenStack告警参考
*******.30 ALM-70103 主机VCPU使用数目超过限制.txt,*******.30;70103;ALM;FusionSphere OpenStack告警参考;主机VCPU使用数目超过限制
*******.31 ALM-70104 主机内存使用超过主机总内存.txt,*******.31;70104;ALM;FusionSphere OpenStack告警参考;主机内存使用超过主机总内存
*******.32 ALM-70105 异构VMware虚拟机卷数据残留告警.txt,*******.32;70105;ALM;FusionSphere OpenStack告警参考;异构VMware虚拟机卷数据残留告警
*******.33 ALM-70106 虚拟机HA中间态告警.txt,*******.33;70106;ALM;FusionSphere OpenStack告警参考;虚拟机HA中间态告警
*******.34 ALM-70108 虚拟机目录文件异常.txt,*******.34;70108;ALM;FusionSphere OpenStack告警参考;虚拟机目录文件异常
*******.35 ALM-70109 虚拟机BDM残留审计告警.txt,*******.35;70109;ALM;FusionSphere OpenStack告警参考;虚拟机BDM残留审计告警
*******.36 ALM-70111 本地盘ERROR状态告警.txt,*******.36;70111;ALM;FusionSphere OpenStack告警参考;本地盘ERROR状态告警
*******.37 ALM-70112 IB网卡故障.txt,*******.37;70112;ALM;FusionSphere OpenStack告警参考;IB网卡故障
*******.38 ALM-70113 NVME SSD盘或卡故障.txt,*******.38;70113;ALM;FusionSphere OpenStack告警参考;NVME SSD盘或卡故障
*******.39 ALM-70126 异构VMware野虚拟机残留告警.txt,*******.39;70126;ALM;FusionSphere OpenStack告警参考;异构VMware野虚拟机残留告警
*******.4 ALM-6015 NTP服务器与外部时钟源网络故障或外部时钟源故障.txt,*******.4;6015;ALM;FusionSphere OpenStack告警参考;NTP服务器与外部时钟源网络故障或外部时钟源故障
*******.40 ALM-70127 主机组内NVMe SSD大盘使用率超过阈值.txt,*******.40;70127;ALM;FusionSphere OpenStack告警参考;主机组内NVMe SSD大盘使用率超过阈值
*******.41 ALM-70128 主机组内NVMe SSD小盘使用率超过阈值.txt,*******.41;70128;ALM;FusionSphere OpenStack告警参考;主机组内NVMe SSD小盘使用率超过阈值
*******.42 ALM-70129 主机组内GPU使用率超过阈值.txt,*******.42;70129;ALM;FusionSphere OpenStack告警参考;主机组内GPU使用率超过阈值
*******.43 ALM-70130 虚拟机HA失败.txt,*******.43;70130;ALM;FusionSphere OpenStack告警参考;虚拟机HA失败
*******.44 ALM-70131 主机组CPU分配率超过阈值.txt,*******.44;70131;ALM;FusionSphere OpenStack告警参考;主机组CPU分配率超过阈值
*******.45 ALM-70132 主机组内存分配率超过阈值.txt,*******.45;70132;ALM;FusionSphere OpenStack告警参考;主机组内存分配率超过阈值
*******.46 ALM-70135 内存复用率超过阈值告警.txt,*******.46;70135;ALM;FusionSphere OpenStack告警参考;内存复用率超过阈值告警
*******.47 ALM-70201 DHCP-agent管理的network数量超过阈值.txt,*******.47;70201;ALM;DHCP-agent管理的network数量超过阈值;FusionSphere OpenStack告警参考
*******.48 ALM-70203 主机上虚拟端口上线处理异常.txt,*******.48;70203;ALM;FusionSphere OpenStack告警参考;主机上虚拟端口上线处理异常
*******.49 ALM-70251 聚合网口状态异常.txt,*******.49;70251;ALM;FusionSphere OpenStack告警参考;聚合网口状态异常
*******.5 ALM-6016 日志服务连接OBS失败.txt,*******.5;6016;ALM;FusionSphere OpenStack告警参考;日志服务连接OBS失败
*******.50 ALM-70300 卷审计告警.txt,*******.50;70300;ALM;FusionSphere OpenStack告警参考;卷审计告警
*******.51 ALM-70310 快照审计告警.txt,*******.51;70310;ALM;FusionSphere OpenStack告警参考;快照审计告警
*******.52 ALM-70400 镜像审计告警.txt,*******.52;70400;ALM;FusionSphere OpenStack告警参考;镜像审计告警
*******.53 ALM-70401 镜像完整性校验失败.txt,*******.53;70401;ALM;FusionSphere OpenStack告警参考;镜像完整性校验失败
*******.54 ALM-70402 整机快照残留.txt,*******.54;70402;ALM;FusionSphere OpenStack告警参考;整机快照残留
*******.55 ALM-73008 虚拟网络资源审计告警.txt,*******.55;73008;ALM;FusionSphere OpenStack告警参考;虚拟网络资源审计告警
*******.56 ALM-73010 文件系统故障告警.txt,*******.56;73010;ALM;FusionSphere OpenStack告警参考;文件系统故障告警
*******.57 ALM-73011 关键进程异常告警.txt,*******.57;73011;ALM;FusionSphere OpenStack告警参考;关键进程异常告警
*******.58 ALM-73012 磁盘分区inode资源不足告警.txt,*******.58;73012;ALM;FusionSphere OpenStack告警参考;磁盘分区inode资源不足告警
*******.59 ALM-73013 存储磁盘IO时延过大告警.txt,*******.59;73013;ALM;FusionSphere OpenStack告警参考;存储磁盘IO时延过大告警
*******.6 ALM-6017 主机状态异常.txt,*******.6;6017;ALM;FusionSphere OpenStack告警参考;主机状态异常
*******.60 ALM-73014 系统文件完整性异常.txt,*******.60;73014;ALM;FusionSphere OpenStack告警参考;系统文件完整性异常
*******.61 ALM-73015 大页内存不足.txt,*******.61;73015;ALM;FusionSphere OpenStack告警参考;大页内存不足
*******.62 ALM-73016 CPU主频异常.txt,*******.62;73016;ALM;CPU主频异常;FusionSphere OpenStack告警参考
*******.63 ALM-73017 主机系统用户密码过期预警.txt,*******.63;73017;ALM;FusionSphere OpenStack告警参考;主机系统用户密码过期预警
*******.64 ALM-73018 swap分区IO时延过大.txt,*******.64;73018;ALM;FusionSphere OpenStack告警参考;swap分区IO时延过大
*******.65 ALM-73019 主机进程数异常.txt,*******.65;73019;ALM;FusionSphere OpenStack告警参考;主机进程数异常
*******.66 ALM-73102 虚拟网络端口错包率超过告警阈值告警.txt,*******.66;73102;ALM;FusionSphere OpenStack告警参考;虚拟网络端口错包率超过告警阈值告警
*******.67 ALM-73104 OVSEVS卡死.txt,*******.67;73104;ALM;FusionSphere OpenStack告警参考;OVSEVS卡死
*******.68 ALM-73107 虚拟机发生反复重启故障.txt,*******.68;73107;ALM;FusionSphere OpenStack告警参考;虚拟机发生反复重启故障
*******.69 ALM-73108 虚拟机Watchdog异常告警.txt,*******.69;73108;ALM;FusionSphere OpenStack告警参考;虚拟机Watchdog异常告警
*******.7 ALM-6018 主机CPU占用率超过阈值.txt,*******.7;6018;ALM;FusionSphere OpenStack告警参考;主机CPU占用率超过阈值
*******.70 ALM-73109 UVP关键进程内存占用率超过阈值告警.txt,*******.70;73109;ALM;FusionSphere OpenStack告警参考;UVP关键进程内存占用率超过阈值告警
*******.71 ALM-73110 虚拟网络关键资源不足.txt,*******.71;73110;ALM;FusionSphere OpenStack告警参考;虚拟网络关键资源不足
*******.72 ALM-73111 主机连接跟踪表超过阈值告警.txt,*******.72;73111;ALM;FusionSphere OpenStack告警参考;主机连接跟踪表超过阈值告警
*******.73 ALM-73112 虚拟机网卡异常.txt,*******.73;73112;ALM;FusionSphere OpenStack告警参考;虚拟机网卡异常
*******.74 ALM-73201 HAProxy代理服务不可用.txt,*******.74;73201;ALM;FusionSphere OpenStack告警参考;HAProxy代理服务不可用
*******.75 ALM-73203 组件故障.txt,*******.75;73203;ALM;FusionSphere OpenStack告警参考;组件故障
*******.76 ALM-73204 主机版本不一致告警.txt,*******.76;73204;ALM;FusionSphere OpenStack告警参考;主机版本不一致告警
*******.77 ALM-73205 Iaas层资源配置不一致告警.txt,*******.77;73205;ALM;FusionSphere OpenStack告警参考;Iaas层资源配置不一致告警
*******.78 ALM-73207 主机名重复.txt,*******.78;73207;ALM;FusionSphere OpenStack告警参考;主机名重复
*******.79 ALM-73208 对接外部仲裁服务异常.txt,*******.79;73208;ALM;FusionSphere OpenStack告警参考;对接外部仲裁服务异常
*******.8 ALM-6019 主机内存占用率超过阈值.txt,*******.8;6019;ALM;FusionSphere OpenStack告警参考;主机内存占用率超过阈值
*******.80 ALM-73209 zookeeper健康检查告警.txt,*******.80;73209;ALM;FusionSphere OpenStack告警参考;zookeeper健康检查告警
*******.81 ALM-73210 未配置管理数据备份至第三方服务器.txt,*******.81;73210;ALM;FusionSphere OpenStack告警参考;未配置管理数据备份至第三方服务器
*******.82 ALM-73301 vCenter连接失败.txt,*******.82;73301;ALM;FusionSphere OpenStack告警参考;vCenter连接失败
*******.83 ALM-73302 存在网络未配置VLAN探测平面.txt,*******.83;73302;ALM;FusionSphere OpenStack告警参考;存在网络未配置VLAN探测平面
*******.84 ALM-73303 VRM服务器连接失败.txt,*******.84;73303;ALM;FusionSphere OpenStack告警参考;VRM服务器连接失败
*******.85 ALM-73401 rabbitmq服务故障.txt,*******.85;73401;ALM;FusionSphere OpenStack告警参考;rabbitmq服务故障
*******.86 ALM-73403 gaussdb主备数据不同步.txt,*******.86;73403;ALM;FusionSphere OpenStack告警参考;gaussdb主备数据不同步
*******.87 ALM-73404 检测到gaussdb恶意访问.txt,*******.87;73404;ALM;FusionSphere OpenStack告警参考;检测到gaussdb恶意访问
*******.88 ALM-73405 gaussdb连接数超过阈值.txt,*******.88;73405;ALM;FusionSphere OpenStack告警参考;gaussdb连接数超过阈值
*******.89 ALM-73410 UVP关键进程CPU占用率超过阈值.txt,*******.89;73410;ALM;FusionSphere OpenStack告警参考;UVP关键进程CPU占用率超过阈值
*******.9 ALM-6020 主机逻辑磁盘占用率超过阈值.txt,*******.9;6020;ALM;FusionSphere OpenStack告警参考;主机逻辑磁盘占用率超过阈值
*******.90 ALM-73411 数据库文件损坏.txt,*******.90;73411;ALM;FusionSphere OpenStack告警参考;数据库文件损坏
*******.91 ALM-73412 检测到gaussdb存在长事务.txt,*******.91;73412;ALM;FusionSphere OpenStack告警参考;检测到gaussdb存在长事务
*******.92 ALM-1060047 后端存储虚拟容量使用率超过阈值.txt,1060047;*******.92;ALM;FusionSphere OpenStack告警参考;后端存储虚拟容量使用率超过阈值
*******.93 ALM-1060049 挂载双活卷单边故障告警.txt,1060049;*******.93;ALM;FusionSphere OpenStack告警参考;挂载双活卷单边故障告警
*******.94 ALM-1060050 后端存储证书异常.txt,1060050;*******.94;ALM;FusionSphere OpenStack告警参考;后端存储证书异常
*******.95 ALM-1101315 主机组内本地直通盘使用率超过阈值.txt,1101315;*******.95;ALM;FusionSphere OpenStack告警参考;主机组内本地直通盘使用率超过阈值
*******.96 ALM-1101320 NPU使用率超过阈值.txt,1101320;*******.96;ALM;FusionSphere OpenStack告警参考;NPU使用率超过阈值
*******.97 ALM-1101321 虚拟机存储链路未完全恢复.txt,1101321;*******.97;ALM;FusionSphere OpenStack告警参考;虚拟机存储链路未完全恢复
*******.98 ALM-1126000 裸金属服务器电源状态未获取到.txt,1126000;*******.98;ALM;FusionSphere OpenStack告警参考;裸金属服务器电源状态未获取到
*******.99 ALM-1126001 裸金属服务器管理状态不可用.txt,1126001;*******.99;ALM;FusionSphere OpenStack告警参考;裸金属服务器管理状态不可用
5.2.3.2 Service OM告警参考.txt,5.2.3.2;Service OM告警参考
5.2.3.2.1 ALM-9002 Service OM与SNMP管理站连接异常.txt,5.2.3.2.1;9002;ALM;Service OM与SNMP管理站连接异常;Service OM告警参考
5.2.3.2.10 ALM-9215 系统磁盘使用率过大.txt,5.2.3.2.10;9215;ALM;Service OM告警参考;系统磁盘使用率过大
5.2.3.2.11 ALM-9216 软件订阅与保障年费即将到期.txt,5.2.3.2.11;9216;ALM;Service OM告警参考;软件订阅与保障年费即将到期
5.2.3.2.12 ALM-9217 软件订阅与保障年费已经过期.txt,5.2.3.2.12;9217;ALM;Service OM告警参考;软件订阅与保障年费已经过期
5.2.3.2.13 ALM-9226 Service OM主备倒换功能被禁用.txt,5.2.3.2.13;9226;ALM;Service OM主备倒换功能被禁用;Service OM告警参考
5.2.3.2.14 ALM-9801 Service OM资源异常.txt,5.2.3.2.14;9801;ALM;Service OM告警参考;Service OM资源异常
5.2.3.2.15 ALM-9803 Service OM与内部部件连接异常.txt,5.2.3.2.15;9803;ALM;Service OM与内部部件连接异常;Service OM告警参考
5.2.3.2.16 ALM-9901 Service OM双机倒换.txt,5.2.3.2.16;9901;ALM;Service OM双机倒换;Service OM告警参考
5.2.3.2.17 ALM-9902 Service OM双机心跳中断.txt,5.2.3.2.17;9902;ALM;Service OM双机心跳中断;Service OM告警参考
5.2.3.2.18 ALM-9903 Service OM双机文件同步失败.txt,5.2.3.2.18;9903;ALM;Service OM双机文件同步失败;Service OM告警参考
5.2.3.2.19 ALM-9911 与License Server通信异常.txt,5.2.3.2.19;9911;ALM;Service OM告警参考;与License Server通信异常
5.2.3.2.2 ALM-9201 Service OM与上级时间服务器同步时间失败.txt,5.2.3.2.2;9201;ALM;Service OM与上级时间服务器同步时间失败;Service OM告警参考
5.2.3.2.20 ALM-9912 未配置OpenStack告警上报.txt,5.2.3.2.20;9912;ALM;Service OM告警参考;未配置OpenStack告警上报
5.2.3.2.21 ALM-9913 系统默认证书使用提醒.txt,5.2.3.2.21;9913;ALM;Service OM告警参考;系统默认证书使用提醒
5.2.3.2.22 ALM-9915 证书过期预警.txt,5.2.3.2.22;9915;ALM;Service OM告警参考;证书过期预警
5.2.3.2.23 ALM-9916 OMM-Server服务异常.txt,5.2.3.2.23;9916;ALM;OMM-Server服务异常;Service OM告警参考
5.2.3.2.24 ALM-9917 Service OM虚拟机CPU占用率超过阈值.txt,5.2.3.2.24;9917;ALM;Service OM告警参考;Service OM虚拟机CPU占用率超过阈值
5.2.3.2.25 ALM-9918 Service OM虚拟机内存占用率超过阈值.txt,5.2.3.2.25;9918;ALM;Service OM告警参考;Service OM虚拟机内存占用率超过阈值
5.2.3.2.3 ALM-9203 Service OM服务器时间被修改.txt,5.2.3.2.3;9203;ALM;Service OM告警参考;Service OM服务器时间被修改
5.2.3.2.4 ALM-9204 Service OM与上级时间服务器时间差异过大.txt,5.2.3.2.4;9204;ALM;Service OM与上级时间服务器时间差异过大;Service OM告警参考
5.2.3.2.5 ALM-9206 Service OM数据备份失败.txt,5.2.3.2.5;9206;ALM;Service OM告警参考;Service OM数据备份失败
5.2.3.2.6 ALM-9207 License即将过期.txt,5.2.3.2.6;9207;ALM;License即将过期;Service OM告警参考
5.2.3.2.7 ALM-9208 License已经过期.txt,5.2.3.2.7;9208;ALM;License已经过期;Service OM告警参考
5.2.3.2.8 ALM-9209 当前资源数量大于License许可上限.txt,5.2.3.2.8;9209;ALM;Service OM告警参考;当前资源数量大于License许可上限
5.2.3.2.9 ALM-9210 当前License已失效.txt,5.2.3.2.9;9210;ALM;Service OM告警参考;当前License已失效
5.2.4 云管理.txt,5.2.4;云管理
******* 性能监控.txt,*******;云管理;性能监控
*******.1 ALM-0001000100010001 【自定义监控】网络设备的设备平均CPU利用率阈值.txt,0001000100010001;*******.1;ALM;【自定义监控】网络设备的设备平均CPU利用率阈值;云管理;性能监控
*******.10 ALM-0001000200010003 【自定义监控】接口流入带宽利用率阈值.txt,0001000200010003;*******.10;ALM;【自定义监控】接口流入带宽利用率阈值;云管理;性能监控
*******.11 ALM-0001000200010004 【自定义监控】接口流出带宽利用率阈值.txt,0001000200010004;*******.11;ALM;【自定义监控】接口流出带宽利用率阈值;云管理;性能监控
*******.12 ALM-0001000200020001 【自定义监控】接口接收包丢弃率阈值.txt,0001000200020001;*******.12;ALM;【自定义监控】接口接收包丢弃率阈值;云管理;性能监控
*******.13 ALM-0001000200020002 【自定义监控】接口发送包丢弃率阈值.txt,0001000200020002;*******.13;ALM;【自定义监控】接口发送包丢弃率阈值;云管理;性能监控
*******.14 ALM-0001000200020003 【自定义监控】接口丢弃发送包数阈值.txt,0001000200020003;*******.14;ALM;【自定义监控】接口丢弃发送包数阈值;云管理;性能监控
*******.15 ALM-0001000200020004 【自定义监控】接口丢弃接收包数阈值.txt,0001000200020004;*******.15;ALM;【自定义监控】接口丢弃接收包数阈值;云管理;性能监控
*******.16 ALM-0001000200020005 【自定义监控】接口发送包错误率阈值.txt,0001000200020005;*******.16;ALM;【自定义监控】接口发送包错误率阈值;云管理;性能监控
*******.17 ALM-0001000200020006 【自定义监控】接口接收包错误率阈值.txt,0001000200020006;*******.17;ALM;【自定义监控】接口接收包错误率阈值;云管理;性能监控
*******.18 ALM-0001000200020007 【自定义监控】接口发送包数阈值.txt,0001000200020007;*******.18;ALM;【自定义监控】接口发送包数阈值;云管理;性能监控
*******.19 ALM-0001000200020008 【自定义监控】接口接收包数阈值.txt,0001000200020008;*******.19;ALM;【自定义监控】接口接收包数阈值;云管理;性能监控
*******.2 ALM-0001000100010002 【自定义监控】网络设备的设备平均内存利用率阈值.txt,0001000100010002;*******.2;ALM;【自定义监控】网络设备的设备平均内存利用率阈值;云管理;性能监控
*******.20 ALM-0001000200020009 【自定义监控】接口接收包速率阈值.txt,0001000200020009;*******.20;ALM;【自定义监控】接口接收包速率阈值;云管理;性能监控
*******.21 ALM-000100020002000A 【自定义监控】接口发送包速率阈值.txt,000100020002000;000100020002000A;*******.21;ALM;【自定义监控】接口发送包速率阈值;云管理;性能监控
*******.22 ALM-000100020002000B 【自定义监控】接口发送错误包数阈值.txt,000100020002000;000100020002000B;*******.22;ALM;【自定义监控】接口发送错误包数阈值;云管理;性能监控
*******.23 ALM-000100020002000C 【自定义监控】接口接收错误包数阈值.txt,000100020002000;000100020002000C;*******.23;ALM;【自定义监控】接口接收错误包数阈值;云管理;性能监控
*******.24 ALM-000100020002000D 【自定义监控】接口发送字节数阈值.txt,000100020002000;000100020002000D;*******.24;ALM;【自定义监控】接口发送字节数阈值;云管理;性能监控
*******.25 ALM-000100020002000E 【自定义监控】接口接收字节数阈值.txt,000100020002000;000100020002000E;*******.25;ALM;【自定义监控】接口接收字节数阈值;云管理;性能监控
*******.26 ALM-0001000300010001 【自定义监控】弹性负载均衡的并发连接数阈值.txt,0001000300010001;*******.26;ALM;【自定义监控】弹性负载均衡的并发连接数阈值;云管理;性能监控
*******.27 ALM-0001000300020003 【自定义监控】弹性负载均衡的网络流入流速阈值.txt,0001000300020003;*******.27;ALM;【自定义监控】弹性负载均衡的网络流入流速阈值;云管理;性能监控
*******.28 ALM-0001000300020004 【自定义监控】弹性负载均衡的网络流出流速阈值.txt,0001000300020004;*******.28;ALM;【自定义监控】弹性负载均衡的网络流出流速阈值;云管理;性能监控
*******.29 ALM-0001000400010001 【自定义监控】单板CPU利用率阈值.txt,0001000400010001;*******.29;ALM;【自定义监控】单板CPU利用率阈值;云管理;性能监控
*******.3 ALM-0001000100010003 【自定义监控】网络设备的响应时间阈值.txt,0001000100010003;*******.3;ALM;【自定义监控】网络设备的响应时间阈值;云管理;性能监控
*******.30 ALM-0001000400010002 【自定义监控】单板内存利用率阈值.txt,0001000400010002;*******.30;ALM;【自定义监控】单板内存利用率阈值;云管理;性能监控
*******.31 ALM-0001000500010001 【自定义监控】光口的光模块接收功率阈值.txt,0001000500010001;*******.31;ALM;【自定义监控】光口的光模块接收功率阈值;云管理;性能监控
*******.32 ALM-0001000500010002 【自定义监控】光口的光模块发送功率阈值.txt,0001000500010002;*******.32;ALM;【自定义监控】光口的光模块发送功率阈值;云管理;性能监控
*******.33 ALM-0003000200010001 【自定义监控】电源的电源功率阈值.txt,0003000200010001;*******.33;ALM;【自定义监控】电源的电源功率阈值;云管理;性能监控
*******.34 ALM-0005000100010001 【自定义监控】宿主机的CPU使用率阈值.txt,0005000100010001;*******.34;ALM;【自定义监控】宿主机的CPU使用率阈值;云管理;性能监控
*******.35 ALM-0005000100020008 【自定义监控】宿主机的内存使用率阈值.txt,0005000100020008;*******.35;ALM;【自定义监控】宿主机的内存使用率阈值;云管理;性能监控
*******.36 ALM-000500010003000B 【自定义监控】宿主机的网络流入速率阈值.txt,000500010003000;000500010003000B;*******.36;ALM;【自定义监控】宿主机的网络流入速率阈值;云管理;性能监控
*******.37 ALM-000500010003000C 【自定义监控】宿主机的网络流出速率阈值.txt,000500010003000;000500010003000C;*******.37;ALM;【自定义监控】宿主机的网络流出速率阈值;云管理;性能监控
*******.38 ALM-0005000100040014 【自定义监控】宿主机的磁盘使用率阈值.txt,0005000100040014;*******.38;ALM;【自定义监控】宿主机的磁盘使用率阈值;云管理;性能监控
*******.39 ALM-0002000200010001 【自定义监控】弹性云服务器的CPU使用率阈值.txt,0002000200010001;*******.39;ALM;【自定义监控】弹性云服务器的CPU使用率阈值;云管理;性能监控
*******.4 ALM-0001000100010004 【自定义监控】网络设备的当日不可达比率阈值.txt,0001000100010004;*******.4;ALM;【自定义监控】网络设备的当日不可达比率阈值;云管理;性能监控
*******.40 ALM-0002000200020003 【自定义监控】弹性云服务器的内存使用率阈值.txt,0002000200020003;*******.40;ALM;【自定义监控】弹性云服务器的内存使用率阈值;云管理;性能监控
*******.41 ALM-0002000200040004 【自定义监控】弹性云服务器的云硬盘使用率阈值.txt,0002000200040004;*******.41;ALM;【自定义监控】弹性云服务器的云硬盘使用率阈值;云管理;性能监控
*******.42 ALM-0005000200010001 【自定义监控】虚拟机的CPU使用率阈值.txt,0005000200010001;*******.42;ALM;【自定义监控】虚拟机的CPU使用率阈值;云管理;性能监控
*******.43 ALM-0005000200020003 【自定义监控】虚拟机的内存使用率阈值.txt,0005000200020003;*******.43;ALM;【自定义监控】虚拟机的内存使用率阈值;云管理;性能监控
*******.44 ALM-0005000200040004 【自定义监控】虚拟机的云硬盘使用率阈值.txt,0005000200040004;*******.44;ALM;【自定义监控】虚拟机的云硬盘使用率阈值;云管理;性能监控
*******.45 ALM-0005000300010001 【自定义监控】主机组的CPU使用率阈值.txt,0005000300010001;*******.45;ALM;【自定义监控】主机组的CPU使用率阈值;云管理;性能监控
*******.46 ALM-0005000300020007 【自定义监控】主机组的内存使用率阈值.txt,0005000300020007;*******.46;ALM;【自定义监控】主机组的内存使用率阈值;云管理;性能监控
*******.47 ALM-0005000300030010 【自定义监控】主机组的网络流入速率阈值.txt,0005000300030010;*******.47;ALM;【自定义监控】主机组的网络流入速率阈值;云管理;性能监控
*******.48 ALM-0005000300030011 【自定义监控】主机组的网络流出速率阈值.txt,0005000300030011;*******.48;ALM;【自定义监控】主机组的网络流出速率阈值;云管理;性能监控
*******.49 ALM-0007000100010001 【自定义监控】端口性能的速率阈值.txt,0007000100010001;*******.49;ALM;【自定义监控】端口性能的速率阈值;云管理;性能监控
*******.5 ALM-0001000100020003 【自定义监控】网络设备的当前会话新建速率阈值.txt,0001000100020003;*******.5;ALM;【自定义监控】网络设备的当前会话新建速率阈值;云管理;性能监控
*******.50 ALM-0007000100010002 【自定义监控】端口性能的CRC错误阈值.txt,0007000100010002;*******.50;ALM;【自定义监控】端口性能的CRC错误阈值;云管理;性能监控
*******.51 ALM-0007000100010003 【自定义监控】端口性能的发送link reset错误阈值.txt,0007000100010003;*******.51;ALM;【自定义监控】端口性能的发送link reset错误阈值;云管理;性能监控
*******.52 ALM-0007000100010004 【自定义监控】端口性能的接收link reset错误阈值.txt,0007000100010004;*******.52;ALM;【自定义监控】端口性能的接收link reset错误阈值;云管理;性能监控
*******.53 ALM-0007000100010005 【自定义监控】端口性能的link reset错误总数阈值.txt,0007000100010005;*******.53;ALM;【自定义监控】端口性能的link reset错误总数阈值;云管理;性能监控
*******.54 ALM-0007000100010006 【自定义监控】端口性能的class 3 discard错误阈值.txt,0007000100010006;*******.54;ALM;【自定义监控】端口性能的class 3 discard错误阈值;云管理;性能监控
*******.55 ALM-0007000100010007 【自定义监控】端口性能的sync loss错误阈值.txt,0007000100010007;*******.55;ALM;【自定义监控】端口性能的sync loss错误阈值;云管理;性能监控
*******.56 ALM-0007000100010008 【自定义监控】端口性能的接收利用率阈值.txt,0007000100010008;*******.56;ALM;【自定义监控】端口性能的接收利用率阈值;云管理;性能监控
*******.57 ALM-0007000100010009 【自定义监控】端口性能的缓冲信用量阈值.txt,0007000100010009;*******.57;ALM;【自定义监控】端口性能的缓冲信用量阈值;云管理;性能监控
*******.58 ALM-000700010001000A 【自定义监控】端口性能的接收速率阈值.txt,000700010001000;000700010001000A;*******.58;ALM;【自定义监控】端口性能的接收速率阈值;云管理;性能监控
*******.59 ALM-000700010001000B 【自定义监控】端口性能的发送利用率阈值.txt,000700010001000;000700010001000B;*******.59;ALM;【自定义监控】端口性能的发送利用率阈值;云管理;性能监控
*******.6 ALM-0001000100020004 【自定义监控】网络设备的当前会话总数阈值.txt,0001000100020004;*******.6;ALM;【自定义监控】网络设备的当前会话总数阈值;云管理;性能监控
*******.60 ALM-000700010001000C 【自定义监控】端口性能的带宽利用率阈值.txt,000700010001000;000700010001000C;*******.60;ALM;【自定义监控】端口性能的带宽利用率阈值;云管理;性能监控
*******.61 ALM-000700010001000D 【自定义监控】端口性能的link failure错误阈值.txt,000700010001000;000700010001000D;*******.61;ALM;【自定义监控】端口性能的link failure错误阈值;云管理;性能监控
*******.62 ALM-000700010001000E 【自定义监控】端口性能的signal loss错误阈值.txt,000700010001000;000700010001000E;*******.62;ALM;【自定义监控】端口性能的signal loss错误阈值;云管理;性能监控
*******.63 ALM-000700010001000F 【自定义监控】端口性能的总error数阈值.txt,000700010001000;000700010001000F;*******.63;ALM;【自定义监控】端口性能的总error数阈值;云管理;性能监控
*******.64 ALM-0007000100010010 【自定义监控】端口性能的发送速率阈值.txt,0007000100010010;*******.64;ALM;【自定义监控】端口性能的发送速率阈值;云管理;性能监控
*******.65 ALM-0008000100010001 【自定义监控】大数据主机组CPU使用率阈值.txt,0008000100010001;*******.65;ALM;【自定义监控】大数据主机组CPU使用率阈值;云管理;性能监控
*******.66 ALM-0008000100010002 【自定义监控】大数据主机组的内存使用率阈.txt,0008000100010002;*******.66;ALM;【自定义监控】大数据主机组的内存使用率阈;云管理;性能监控
*******.67 ALM-0008000100010003 【自定义监控】大数据主机组的磁盘使用率阈值.txt,0008000100010003;*******.67;ALM;【自定义监控】大数据主机组的磁盘使用率阈值;云管理;性能监控
*******.68 ALM-0002000500000001 【自定义监控】弹性IP的上行流量阈值.txt,0002000500000001;*******.68;ALM;【自定义监控】弹性IP的上行流量阈值;云管理;性能监控
*******.69 ALM-0002000500000003 【自定义监控】弹性IP的下行流量阈值.txt,0002000500000003;*******.69;ALM;【自定义监控】弹性IP的下行流量阈值;云管理;性能监控
*******.7 ALM-0001000100020005 【自定义监控】网络设备的网络流量值阈值.txt,0001000100020005;*******.7;ALM;【自定义监控】网络设备的网络流量值阈值;云管理;性能监控
*******.70 ALM-0002000A00000001 【自定义监控】裸金属服务器的用户空间CPU使用率阈值.txt,00000001;0002000;0002000A00000001;*******.70;ALM;【自定义监控】裸金属服务器的用户空间CPU使用率阈值;云管理;性能监控
*******.71 ALM-0002000A0000000D 【自定义监控】裸金属服务器的内存使用率阈值.txt,0000000;0002000;0002000A0000000D;*******.71;ALM;【自定义监控】裸金属服务器的内存使用率阈值;云管理;性能监控
*******.72 ALM-0002000A00000014 【自定义监控】裸金属服务器的磁盘使用率阈值.txt,00000014;0002000;0002000A00000014;*******.72;ALM;【自定义监控】裸金属服务器的磁盘使用率阈值;云管理;性能监控
*******.73 ALM-0002000700000001 【自定义监控】关系数据库的CPU使用率阈值.txt,0002000700000001;*******.73;ALM;【自定义监控】关系数据库的CPU使用率阈值;云管理;性能监控
*******.74 ALM-0002000700000002 【自定义监控】关系数据库的内存使用率阈值.txt,0002000700000002;*******.74;ALM;【自定义监控】关系数据库的内存使用率阈值;云管理;性能监控
*******.75 ALM-0002000700000003 【自定义监控】关系数据库的IOPS阈值.txt,0002000700000003;*******.75;ALM;【自定义监控】关系数据库的IOPS阈值;云管理;性能监控
*******.76 ALM-000200070000002A 【自定义监控】关系数据库的数据库连接数阈值.txt,000200070000002;000200070000002A;*******.76;ALM;【自定义监控】关系数据库的数据库连接数阈值;云管理;性能监控
*******.77 ALM-0002000700000036 【自定义监控】关系数据库的使用中的数据库连接数阈值告警.txt,0002000700000036;*******.77;ALM;【自定义监控】关系数据库的使用中的数据库连接数阈值告警;云管理;性能监控
*******.78 ALM-0002000800000003 【自定义监控】Oracle CDB关系型数据库的主机内存使用率阈值.txt,0002000800000003;*******.78;ALM;【自定义监控】Oracle CDB关系型数据库的主机内存使用率阈值;云管理;性能监控
*******.79 ALM-0002000800000004 【自定义监控】Oracle CDB关系型数据库的主机CPU使用率阈值.txt,0002000800000004;*******.79;ALM;【自定义监控】Oracle CDB关系型数据库的主机CPU使用率阈值;云管理;性能监控
*******.8 ALM-0001000200010001 【自定义监控】接口接收速率阈值.txt,0001000200010001;*******.8;ALM;【自定义监控】接口接收速率阈值;云管理;性能监控
*******.80 ALM-0002000900000003 【自定义监控】Oracle PDB关系型数据库的主机内存使用率阈值.txt,0002000900000003;*******.80;ALM;【自定义监控】Oracle PDB关系型数据库的主机内存使用率阈值;云管理;性能监控
*******.81 ALM-0002000900000004 【自定义监控】Oracle PDB关系型数据库的主机CPU使用率阈值.txt,0002000900000004;*******.81;ALM;【自定义监控】Oracle PDB关系型数据库的主机CPU使用率阈值;云管理;性能监控
*******.82 ALM-0002000700000048 【自定义监控】关系数据库的CPU使用率超过阈值.txt,0002000700000048;*******.82;ALM;【自定义监控】关系数据库的CPU使用率超过阈值;云管理;性能监控
*******.83 ALM-0002000700000049 【自定义监控】关系数据库的内存使用率超过阈值.txt,0002000700000049;*******.83;ALM;【自定义监控】关系数据库的内存使用率超过阈值;云管理;性能监控
*******.84 ALM-0003000200010005 【自定义监控】电源的电源功率超过阈值.txt,0003000200010005;*******.84;ALM;【自定义监控】电源的电源功率超过阈值;云管理;性能监控
*******.85 ALM-000A000200000010 【自定义监控】分布式缓存服务的每秒并发操作数超过阈值.txt,000;000200000010;000A000200000010;*******.85;ALM;【自定义监控】分布式缓存服务的每秒并发操作数超过阈值;云管理;性能监控
*******.86 ALM-000A000200000021 【自定义监控】分布式缓存服务的缓存命中率超过阈值.txt,000;000200000021;000A000200000021;*******.86;ALM;【自定义监控】分布式缓存服务的缓存命中率超过阈值;云管理;性能监控
*******.87 ALM-000A00020000002D 【自定义监控】分布式缓存服务的每秒并发操作数超过阈值.txt,000;00020000002;000A00020000002D;*******.87;ALM;【自定义监控】分布式缓存服务的每秒并发操作数超过阈值;云管理;性能监控
*******.88 ALM-000A00020000004D 【自定义监控】分布式缓存服务的CPU平均利用率超过阈值.txt,000;00020000004;000A00020000004D;*******.88;ALM;【自定义监控】分布式缓存服务的CPU平均利用率超过阈值;云管理;性能监控
*******.9 ALM-0001000200010002 【自定义监控】接口发送速率阈值.txt,0001000200010002;*******.9;ALM;【自定义监控】接口发送速率阈值;云管理;性能监控
*******0 系统维护.txt,*******0;云管理;系统维护
*******0.1 告警参考.txt,*******0.1;云管理;告警参考;系统维护
*******0.1.1 ALM-MOMaintenanceService_100100 操作系统帐号密码即将过期.txt,100100;*******0.1.1;ALM;MOMaintenanceService_100100;云管理;告警参考;操作系统帐号密码即将过期;系统维护
*******0.1.2 ALM-MOMaintenanceService_100103 证书即将过期.txt,100103;*******0.1.2;ALM;MOMaintenanceService_100103;云管理;告警参考;系统维护;证书即将过期
*******0.1.3 ALM-MOMaintenanceService_100106 证书即将过期.txt,100106;*******0.1.3;ALM;MOMaintenanceService_100106;云管理;告警参考;系统维护;证书即将过期
*******1 服务监控.txt,*******1;云管理;服务监控
*******1.1 告警参考.txt,*******1.1;云管理;告警参考;服务监控
*******1.1.1 ALM-servicemonitor_agent_heartbeat 节点连续中断.txt,*******1.1.1;ALM;servicemonitor_agent_heartbeat;云管理;告警参考;服务监控;节点连续中断
*******1.1.10 ALM-servicemonitor_os.disk.rd_rsp_time 硬盘读响应时间阈值告警.txt,.disk.rd_rsp_time 硬盘读响应时间阈值告警;*******1.1.10;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******1.1.11 ALM-servicemonitor_os.disk.wt_rsp_time 硬盘写响应时间阈值告警.txt,.disk.wt_rsp_time 硬盘写响应时间阈值告警;*******1.1.11;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******1.1.12 ALM-servicemonitor_os.fs.inode_free inode空闲率阈值告警.txt,.fs.inode_free inode空闲率阈值告警;*******1.1.12;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******1.1.13 ALM-servicemonitor_os.fs.percent 硬盘使用率阈值告警.txt,.fs.percent 硬盘使用率阈值告警;*******1.1.13;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******1.1.14 ALM-servicemonitor_redis.dbcopyStatus Redis数据库实例复制状态阈值告警.txt,.dbcopyStatus Redis数据库实例复制状态阈值告警;*******1.1.14;ALM;servicemonitor_redis;云管理;告警参考;服务监控
*******1.1.15 ALM-servicemonitor_redis.dbsvrStatus Redis数据库实例状态阈值告警.txt,.dbsvrStatus Redis数据库实例状态阈值告警;*******1.1.15;ALM;servicemonitor_redis;云管理;告警参考;服务监控
*******1.1.16 ALM-servicemonitor_redis.connectedClientsRate Redis数据库实例的客户端连接使用率阈值告警.txt,.connectedClientsRate Redis数据库实例的客户端连接使用率阈值告警;*******1.1.16;ALM;servicemonitor_redis;云管理;告警参考;服务监控
*******1.1.2 ALM-servicemonitor_heartbeat 服务监控节点心跳异常告警.txt,*******1.1.2;ALM;servicemonitor_heartbeat;云管理;告警参考;服务监控;服务监控节点心跳异常告警
*******1.1.3 ALM-servicemonitor_cpu.percent CPU使用率阈值告警.txt,.percent CPU使用率阈值告警;*******1.1.3;ALM;servicemonitor_cpu;云管理;告警参考;服务监控
*******1.1.4 ALM-servicemonitor_memory.percent 物理内存使用率阈值告警.txt,.percent 物理内存使用率阈值告警;*******1.1.4;ALM;servicemonitor_memory;云管理;告警参考;服务监控
*******1.1.5 ALM-servicemonitor_os.nic.rx_dropped_ps 网卡流入丢包率阈值告警.txt,.nic.rx_dropped_ps 网卡流入丢包率阈值告警;*******1.1.5;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******1.1.6 ALM-servicemonitor_os.nic.rx_errors_ps 网卡流入错包率阈值告警.txt,.nic.rx_errors_ps 网卡流入错包率阈值告警;*******1.1.6;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******1.1.7 ALM-servicemonitor_os.nic.tx_dropped 网卡流出丢包率阈值告警.txt,.nic.tx_dropped 网卡流出丢包率阈值告警;*******1.1.7;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******1.1.8 ALM-servicemonitor_os.nic.tx_errors 网卡流出错包率阈值告警.txt,.nic.tx_errors 网卡流出错包率阈值告警;*******1.1.8;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******1.1.9 ALM-servicemonitor_os.disk.io_waite 硬盘IO等待时间阈值告警.txt,.disk.io_waite 硬盘IO等待时间阈值告警;*******1.1.9;ALM;servicemonitor_os;云管理;告警参考;服务监控
*******2 统一证书.txt,*******2;云管理;统一证书
*******2.1 ALM-MOCertMgmt_100101 系统存在即将过期证书告警.txt,100101;*******2.1;ALM;MOCertMgmt_100101;云管理;系统存在即将过期证书告警;统一证书
*******3 统一日志.txt,*******3;云管理;统一日志
*******3.1 ALM-0001000300030001 Elasticsearch集群心跳检测异常.txt,0001000300030001;*******3.1;ALM;Elasticsearch集群心跳检测异常;云管理;统一日志
*******4 备份恢复.txt,*******4;云管理;备份恢复
*******4.1 ALM-MOBackupService_100001 备份失败.txt,100001;*******4.1;ALM;MOBackupService_100001;云管理;备份失败;备份恢复
*******4.2 ALM-MOBackupService_100002 未配置备份服务器.txt,100002;*******4.2;ALM;MOBackupService_100002;云管理;备份恢复;未配置备份服务器
*******5 IES管理.txt,*******5;IES管理;云管理
*******5.1 ALM-101205 产品数据定时备份失败.txt,101205;*******5.1;ALM;IES管理;云管理;产品数据定时备份失败
*******6 运维自动化.txt,*******6;云管理;运维自动化
*******6.1 ALM-10000011 ManageOne管理资源超过部署规格限制.txt,10000011;*******6.1;ALM;ManageOne管理资源超过部署规格限制;云管理;运维自动化
*******7 华为虚拟化资源池.txt,*******7;云管理;华为虚拟化资源池
*******7.1 ALM-MOVCDRService_100091 话单文件发送到计量中心文件服务器失败告警.txt,100091;*******7.1;ALM;MOVCDRService_100091;云管理;华为虚拟化资源池;话单文件发送到计量中心文件服务器失败告警
*******8 部署面业务告警.txt,*******8;云管理;部署面业务告警
*******8.1 ManageOne管理.txt,*******8.1;ManageOne管理;云管理;部署面业务告警
*******8.1.1 ALM-101209 节点故障倒换.txt,101209;*******8.1.1;ALM;ManageOne管理;云管理;节点故障倒换;部署面业务告警
*******8.1.2 ALM-51023 NTP服务异常.txt,*******8.1.2;51023;ALM;ManageOne管理;NTP服务异常;云管理;部署面业务告警
*******8.1.3 ALM-101211 数据库实例故障倒换.txt,101211;*******8.1.3;ALM;ManageOne管理;云管理;数据库实例故障倒换;部署面业务告警
*******8.1.4 ALM-101212 连接ZooKeeper失败.txt,101212;*******8.1.4;ALM;ManageOne管理;云管理;连接ZooKeeper失败;部署面业务告警
*******8.2 系统监控.txt,*******8.2;云管理;系统监控;部署面业务告警
*******8.2.1 ALM-151 CPU占用率过高告警.txt,151;*******8.2.1;ALM;CPU占用率过高告警;云管理;系统监控;部署面业务告警
*******8.2.10 ALM-101210 数据库本地主备复制异常.txt,101210;*******8.2.10;ALM;云管理;数据库本地主备复制异常;系统监控;部署面业务告警
*******8.2.2 ALM-152 网管服务异常退出告警.txt,152;*******8.2.2;ALM;云管理;系统监控;网管服务异常退出告警;部署面业务告警
*******8.2.3 ALM-101208 节点状态异常.txt,101208;*******8.2.3;ALM;云管理;系统监控;节点状态异常;部署面业务告警
*******8.2.4 ALM-154 内存占用率过高告警.txt,154;*******8.2.4;ALM;云管理;内存占用率过高告警;系统监控;部署面业务告警
*******8.2.5 ALM-36 磁盘占用率过高告警.txt,36;*******8.2.5;ALM;云管理;磁盘占用率过高告警;系统监控;部署面业务告警
*******8.2.6 ALM-38 数据库进程异常.txt,38;*******8.2.6;ALM;云管理;数据库进程异常;系统监控;部署面业务告警
*******8.2.7 ALM-47 服务内存占用过高告警.txt,47;*******8.2.7;ALM;云管理;服务内存占用过高告警;系统监控;部署面业务告警
*******8.2.8 ALM-101206 SSH管理通道故障.txt,101206;*******8.2.8;ALM;SSH管理通道故障;云管理;系统监控;部署面业务告警
*******8.2.9 ALM-53080 部署面服务进程资源占用异常.txt,*******8.2.9;53080;ALM;云管理;系统监控;部署面业务告警;部署面服务进程资源占用异常
*******8.3 安全管理.txt,*******8.3;云管理;安全管理;部署面业务告警
*******8.3.1 ALM-51020 证书即将过期.txt,*******8.3.1;51020;ALM;云管理;安全管理;证书即将过期;部署面业务告警
*******8.3.2 ALM-51021 证书已经过期.txt,*******8.3.2;51021;ALM;云管理;安全管理;证书已经过期;部署面业务告警
*******8.3.3 ALM-51022 证书更新失败.txt,*******8.3.3;51022;ALM;云管理;安全管理;证书更新失败;部署面业务告警
*******8.4 参考信息.txt,*******8.4;云管理;参考信息;部署面业务告警
*******8.4.1 如何查找节点对应的IP地址.txt,*******8.4.1;云管理;参考信息;如何查找节点对应的IP地址;部署面业务告警
*******9 IAM 告警参考.txt,*******9;IAM 告警参考;云管理
*******9.1 ALM-36064531474581504 IAM数据库资源使用异常或服务异常.txt,36064531474581504;*******9.1;ALM;IAM 告警参考;IAM数据库资源使用异常或服务异常;云管理
*******9.2 ALM-0004000700010009 IAM鉴权失败.txt,0004000700010009;*******9.2;ALM;IAM 告警参考;IAM鉴权失败;云管理
******* 驱动管理.txt,*******;云管理;驱动管理
*******.1 ALM-100502 系统连通性检测失败告警.txt,100502;*******.1;ALM;云管理;系统连通性检测失败告警;驱动管理
*******.2 ALM-100553 SNMP连通性检测失败告警.txt,100553;*******.2;ALM;SNMP连通性检测失败告警;云管理;驱动管理
******* 驱动框架.txt,*******;云管理;驱动框架
*******.1 ALM-100550 LVS配置失败告警.txt,100550;*******.1;ALM;LVS配置失败告警;云管理;驱动框架
*******.2 ALM-100551 LVS中断服务告警.txt,100551;*******.2;ALM;LVS中断服务告警;云管理;驱动框架
******* 容量管理.txt,*******;云管理;容量管理
*******.1 ALM-CloudCapacityMgmt_Base_1001 vCPU分配率超过阈值告警.txt,*******.1;ALM;CloudCapacityMgmt_Base_1001;vCPU分配率超过阈值告警;云管理;容量管理
*******.2 ALM-CloudCapacityMgmt_Base_1002 vMemory分配率超过阈值告警.txt,*******.2;ALM;CloudCapacityMgmt_Base_1002;vMemory分配率超过阈值告警;云管理;容量管理
*******.3 ALM-CloudCapacityMgmt_Base_1003 存储使用率超过阈值告警.txt,*******.3;ALM;CloudCapacityMgmt_Base_1003;云管理;存储使用率超过阈值告警;容量管理
*******.4 ALM-CloudCapacityMgmt_Base_1004 存储分配率超过阈值告警.txt,*******.4;ALM;CloudCapacityMgmt_Base_1004;云管理;存储分配率超过阈值告警;容量管理
*******.5 ALM-CloudCapacityMgmt_Base_1005 弹性IP使用率超过阈值告警.txt,*******.5;ALM;CloudCapacityMgmt_Base_1005;云管理;容量管理;弹性IP使用率超过阈值告警
*******.6 ALM-CloudCapacityMgmt_Base_1006 数据存储使用率超过阈值告警.txt,*******.6;ALM;CloudCapacityMgmt_Base_1006;云管理;容量管理;数据存储使用率超过阈值告警
******* 告警管理.txt,*******;云管理;告警管理
*******.1 ALM-157 当前告警数量达到阈值.txt,157;*******.1;ALM;云管理;告警管理;当前告警数量达到阈值
*******.2 ALM-832 同类告警数量超出门限.txt,*******.2;832;ALM;云管理;同类告警数量超出门限;告警管理
******* 安全管理.txt,*******;云管理;安全管理
*******.1 ALM-128 远端认证主服务器连接失败告警.txt,128;*******.1;ALM;云管理;安全管理;远端认证主服务器连接失败告警
*******.2 ALM-160 远端认证备服务器连接失败告警.txt,160;*******.2;ALM;云管理;安全管理;远端认证备服务器连接失败告警
*******.3 ALM-30004 用户密码即将过期.txt,30004;*******.3;ALM;云管理;安全管理;用户密码即将过期
*******.4 ALM-30005 用户密码已过期.txt,30005;*******.4;ALM;云管理;安全管理;用户密码已过期
*******.5 ALM-505001106 登录尝试次数达到最大值.txt,*******.5;505001106;ALM;云管理;安全管理;登录尝试次数达到最大值
5.2.4.7 日志转发.txt,5.2.4.7;云管理;日志转发
5.2.4.7.1 ALM-126 切换到备系统日志服务器告警.txt,126;5.2.4.7.1;ALM;云管理;切换到备系统日志服务器告警;日志转发
5.2.4.7.2 ALM-127 连接主、备系统日志服务器均失败告警.txt,127;5.2.4.7.2;ALM;云管理;日志转发;连接主、备系统日志服务器均失败告警
5.2.4.8 License管理.txt,5.2.4.8;License管理;云管理
5.2.4.8.1 ALM-999999992 License即将过期.txt,5.2.4.8.1;999999992;ALM;License即将过期;License管理;云管理
5.2.4.8.10 ALM-999999999 License控制项或销售项进入宽限期.txt,5.2.4.8.10;999999999;ALM;License控制项或销售项进入宽限期;License管理;云管理
5.2.4.8.2 ALM-999999993 License已经过期.txt,5.2.4.8.2;999999993;ALM;License已经过期;License管理;云管理
5.2.4.8.3 ALM-999999995 License不合法.txt,5.2.4.8.3;999999995;ALM;License不合法;License管理;云管理
5.2.4.8.4 ALM-999999989 软件服务年费即将过期.txt,5.2.4.8.4;999999989;ALM;License管理;云管理;软件服务年费即将过期
5.2.4.8.5 ALM-999999990 软件服务年费已经过期.txt,5.2.4.8.5;999999990;ALM;License管理;云管理;软件服务年费已经过期
5.2.4.8.6 ALM-999999994 License资源达到或超过阈值.txt,5.2.4.8.6;999999994;ALM;License管理;License资源达到或超过阈值;云管理
5.2.4.8.7 ALM-999999996 License销售项达到或超过阈值.txt,5.2.4.8.7;999999996;ALM;License管理;License销售项达到或超过阈值;云管理
5.2.4.8.8 ALM-999999997 License资源达到或超过100%阈值.txt,5.2.4.8.8;999999997;ALM;License管理;License资源达到或超过100%阈值;云管理
5.2.4.8.9 ALM-999999998 License销售项达到或超过100%阈值.txt,5.2.4.8.9;999999998;ALM;License管理;License销售项达到或超过100%阈值;云管理
5.2.4.9 远程通知管理.txt,5.2.4.9;云管理;远程通知管理
5.2.4.9.1 ALM-505001111 远程通知发送失败.txt,5.2.4.9.1;505001111;ALM;云管理;远程通知发送失败;远程通知管理
5.2.6 组合API.txt,5.2.6;组合API
5.2.6.1 组合API.txt,5.2.6.1;组合API
5.2.6.1.1 ALM-1150001 组合API节点NTP进程状态异常.txt,1150001;5.2.6.1.1;ALM;组合API;组合API节点NTP进程状态异常
5.2.6.1.2 ALM-1150002 组合API节点CPU使用率过高.txt,1150002;5.2.6.1.2;ALM;组合API;组合API节点CPU使用率过高
5.2.6.1.3 ALM-1150003 组合API节点内存使用率过高.txt,1150003;5.2.6.1.3;ALM;组合API;组合API节点内存使用率过高
5.2.6.1.4 ALM-1150004 组合API节点tomcat进程异常.txt,1150004;5.2.6.1.4;ALM;组合API;组合API节点tomcat进程异常
5.2.6.1.5 ALM-1150017 组合API节点tomcat进程不存在.txt,1150017;5.2.6.1.5;ALM;组合API;组合API节点tomcat进程不存在
5.2.6.1.6 ALM-1150018 组合API节点tomcat存在多进程.txt,1150018;5.2.6.1.6;ALM;组合API;组合API节点tomcat存在多进程
5.2.6.2 云硬盘.txt,5.2.6.2;云硬盘;组合API
5.2.6.2.1 1060036 evs周期性检测cinder连通性失败.txt,1060036;5.2.6.2.1;evs周期性检测cinder连通性失败;云硬盘;组合API
5.2.6.2.2 修改配置文件中对接其他组件或服务的帐户密码.txt,5.2.6.2.2;云硬盘;修改配置文件中对接其他组件或服务的帐户密码;组合API
5.2.6.2.3 典型Cinder问题定位指导.txt,5.2.6.2.3;云硬盘;典型Cinder问题定位指导;组合API
5.2.6.2.3.1 排查FusionSphere OpenStack.txt,5.2.6.2.3.1;云硬盘;典型Cinder问题定位指导;排查FusionSphere OpenStack;组合API
5.2.6.2.3.1.1 查询对应卷信息.txt,5.2.6.2.3.1.1;云硬盘;典型Cinder问题定位指导;排查FusionSphere OpenStack;查询对应卷信息;组合API
5.2.6.2.3.1.2 查看cinder-api日志.txt,5.2.6.2.3.1.2;云硬盘;典型Cinder问题定位指导;排查FusionSphere OpenStack;查看cinder-api日志;组合API
5.2.6.2.3.1.3 查看cinder-scheduler日志.txt,5.2.6.2.3.1.3;云硬盘;典型Cinder问题定位指导;排查FusionSphere OpenStack;查看cinder-scheduler日志;组合API
5.2.6.2.3.1.4 查询cinder-volume日志.txt,5.2.6.2.3.1.4;云硬盘;典型Cinder问题定位指导;排查FusionSphere OpenStack;查询cinder-volume日志;组合API
5.2.6.2.3.2 排查被级联层OpenStack.txt,5.2.6.2.3.2;云硬盘;典型Cinder问题定位指导;排查被级联层OpenStack;组合API
5.2.6.2.3.2.1 查看被级联层cinder-api日志.txt,5.2.6.2.3.2.1;云硬盘;典型Cinder问题定位指导;排查被级联层OpenStack;查看被级联层cinder-api日志;组合API
5.2.6.2.3.2.2 查询被级联层cinder-scheduler日志.txt,5.2.6.2.3.2.2;云硬盘;典型Cinder问题定位指导;排查被级联层OpenStack;查询被级联层cinder-scheduler日志;组合API
5.2.6.2.3.2.3 查询被级联层cinder-volume日志.txt,5.2.6.2.3.2.3;云硬盘;典型Cinder问题定位指导;排查被级联层OpenStack;查询被级联层cinder-volume日志;组合API
5.2.6.3 镜像服务.txt,5.2.6.3;组合API;镜像服务
5.2.6.3.1 告警参考.txt,5.2.6.3.1;告警参考;组合API;镜像服务
5.2.6.3.1.1 ALM-1131007 ntp进程不存在.txt,1131007;5.2.6.3.1.1;ALM;ntp进程不存在;告警参考;组合API;镜像服务
5.2.6.3.1.2 ALM-1131009 tomcat多进程.txt,1131009;5.2.6.3.1.2;ALM;tomcat多进程;告警参考;组合API;镜像服务
5.2.6.3.1.3 ALM-1131011 tomcat进程不存在.txt,1131011;5.2.6.3.1.3;ALM;tomcat进程不存在;告警参考;组合API;镜像服务
5.2.6.3.1.4 ALM-1131012 tomcat进程down掉.txt,1131012;5.2.6.3.1.4;ALM;tomcat进程down掉;告警参考;组合API;镜像服务
5.2.6.4 弹性云服务器.txt,5.2.6.4;弹性云服务器;组合API
5.2.6.4.1 ALM-1101308 连接ECS数据库失败.txt,1101308;5.2.6.4.1;ALM;弹性云服务器;组合API;连接ECS数据库失败
5.2.6.4.2 ALM-1101312 ECS连接FSP失败.txt,1101312;5.2.6.4.2;ALM;ECS连接FSP失败;弹性云服务器;组合API
5.2.6.4.3 登录FusionSphere OpenStack后台.txt,5.2.6.4.3;弹性云服务器;登录FusionSphere OpenStack后台;组合API
5.2.6.4.4 导入环境变量.txt,5.2.6.4.4;导入环境变量;弹性云服务器;组合API
*******.1 OBS Console.txt,*******.1;OBS Console
*******.1.1 ALM-600000007 OBS Console 的tomcat进程异常.txt,*******.1.1;600000007;ALM;OBS Console;OBS Console 的tomcat进程异常
*******.1.2 ALM-600000008 OBS Console 的tomcat端口未监听.txt,*******.1.2;600000008;ALM;OBS Console;OBS Console 的tomcat端口未监听
*******.1.3 ALM-600000100 OBS Console tomcat证书异常.txt,*******.1.3;600000100;ALM;OBS Console;OBS Console tomcat证书异常
*******.3 OBS LVS.txt,*******.3;OBS LVS
*******.3.1 ALM-600000201 OBS LVS节点存在未连通网口.txt,*******.3.1;600000201;ALM;OBS LVS;OBS LVS节点存在未连通网口
*******.3.2 ALM-600000200 keepalived进程未启动.txt,*******.3.2;600000200;ALM;OBS LVS;keepalived进程未启动
5.2.9 虚拟私有云.txt,5.2.9;虚拟私有云
******* ALM-1200025 ntp进程故障.txt,1200025;*******;ALM;ntp进程故障;虚拟私有云
*******0 ALM-1200054 公网IP地址不足.txt,1200054;*******0;ALM;公网IP地址不足;虚拟私有云
*******1 ALM-1200074 VPC数据库访问失败.txt,1200074;*******1;ALM;VPC数据库访问失败;虚拟私有云
******** 附录.txt,********;虚拟私有云;附录
********.1 导入环境变量.txt,********.1;导入环境变量;虚拟私有云;附录
******* ALM-1200027 tomcat进程CPU占用率超过阈值.txt,1200027;*******;ALM;tomcat进程CPU占用率超过阈值;虚拟私有云
******* ALM-1200028 tomcat进程内存占用率超过阈值.txt,1200028;*******;ALM;tomcat进程内存占用率超过阈值;虚拟私有云
******* ALM-1200030 tomcat进程无响应.txt,1200030;*******;ALM;tomcat进程无响应;虚拟私有云
******* ALM-1200032 zookeeper进程CPU占用率超过阈值.txt,1200032;*******;ALM;zookeeper进程CPU占用率超过阈值;虚拟私有云
******* ALM-1200033 zookeeper进程内存占用率超过阈值.txt,1200033;*******;ALM;zookeeper进程内存占用率超过阈值;虚拟私有云
******* ALM-1200034 zookeeper进程重复运行.txt,1200034;*******;ALM;zookeeper进程重复运行;虚拟私有云
5.2.9.8 ALM-1200035 zookeeper进程无响应.txt,1200035;5.2.9.8;ALM;zookeeper进程无响应;虚拟私有云
5.2.9.9 ALM-1200053 Neutron接口调用失败.txt,1200053;5.2.9.9;ALM;Neutron接口调用失败;虚拟私有云
