import requests
import json

# 配置参数
dataset_id = "06ea8a7b-17f1-48d5-9e9f-53c0c6caace8"
document_id = "215502cd-9dc8-4b02-b83b-4e6e9f8a39a2"
document_id = "0d586cdc-b1d2-4cd8-8aca-92321adfe2d1"
document_id = "367c8205-2123-4227-a14a-6c616c5c60ae" ###********.3 ALM-48409-文件句柄使用率超过门限值
segment_id = "754e45bb-6dec-4ede-8dd3-e7cd65e71176"
base_url = f"http://localhost/v1/datasets/{dataset_id}/documents/{document_id}/segments/{segment_id}"
api_key = "dataset-dyT48T6t6lrkKq06EYv3qlKV"  # 替换为你的实际 API Key


# 请求头
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

payload = {
    "segment": {
        "content": "********.3 ALM-48409-文件句柄使用率超过门限值\n\n##### 告警解释\n当API网关节点的文件句柄使用率过高（超过80%），系统产生此告警。\n##### 告警属性\n| 告警ID | 告警级别 | 告警类型 |\n| --- | --- | --- |\n| 48409 | 次要 | 设备告警 |\n##### 告警参数\n| 分类 | 参数名称 | 参数说明 |\n| --- | --- | --- |\n| 定位信息 | CloudService | 云服务名称 |\n| 定位信息 | Node | 故障节点IP地址 |\n| 定位信息 | User_Name | 用户名 |\n| 附加信 | Alarm_Reason | 告警原因 |\n##### 对系统的影响\n文件句柄使用率超过门限值，系统可能会无法创建进程，影响业务性能。\n##### 可能原因\n网关部署所在的服务器文件句柄使用率达到设置的门限值。\n##### 处理步骤\n1. 查看告警的定位信息。\n- 以admin帐号登录ManageOne运维面。\n登录地址：https://ManageOne运维面主页的访问地址:31943，例如，https://oc.type.com:31943。默认密码：*****。\n- 在页面上方的菜单栏中，选择“集中告警 > 当前告警”。\n- 在告警列表中，找到待处理的告警记录。单击告警对应的“名称”列，弹出“告警详情和处理建议”窗口。\n- 在“告警详情和处理建议”窗口的“基本信息”列表中，查看告警的定位信息。\n- Node：表示告警源节点IP地址。\n- User_Name：表示产生告警的用户名。\n如果未能找到节点信息，返回告警列表，单击告警前的“>”展开详细信息，在“被汇聚告警”页签中可找到告警的定位信息。\n2. 使用PuTTY，登录告警源节点Node。\n默认帐号： paas，默认密码：*****。\n3. 执行以下命令，切换到root用户。\nsudo su - root\n默认密码：*****。\n4. 执行以下命令，防止会话超时退出。\nTMOUT=0\n5. 执行以下命令，切换至User_Name用户。\nsu - User_Name\n6. 执行以下命令，查看当前系统最大句柄设置数，并判断查询结果是否大于“2000000”。\nulimit -n\n- 是 => 8\n- 否 => 7\n7. 执行以下步骤，将系统文件句柄数增加到“2000000”。\n- 执行以下命令，切换到root用户。\nexit\n- 执行以下命令，修改配置文件中用户User_Name的文件句柄数为“2000000”。\n命令中的User_Name需要替换为实际的用户名。\necho \"User_Name soft nofile 2000000\" >> /etc/security/limits.conf\necho \"User_Name hard nofile 2000000\" >> /etc/security/limits.conf\n8. 执行以下命令，将系统文件数句柄数增加到“4000000”。\n- 执行以下命令，切换到root用户。\nexit\n- 执行以下命令，修改配置文件中用户User_Name的文件句柄数为“4000000”。\n命令中的User_Name需要替换为实际的用户名。\necho \"User_Name soft nofile 4000000\" >> /etc/security/limits.conf\necho \"User_Name hard nofile 4000000\" >> /etc/security/limits.conf\n9. 等待1小时，查看告警是否清除。\n- 是 => 处理完毕\n- 否 => 11\n10. 获取相关日志，并联系技术支持。\n- 执行如下命令，切换到日志目录。\ncd /var/log/apigateway/opsagent\n- 下载日志“opsagent.log”到本地，并联系技术支持。\n##### 告警清除\n此告警修复后，系统会自动清除此告警，无需手工清除。\n##### 参考信息\n无。\n< 上一节",
        "keywords": ["48409","文件句柄使用率超过门限值","ALM","********.3"],
    }
}

try:
    # 发送 GET 请求
    response = requests.post(
        base_url.format(dataset_id=dataset_id, document_id=document_id),
        headers=headers,
        data=json.dumps(payload)
    )

    response.raise_for_status()  # 检查请求是否成功

    # 解析并美化打印响应结果
    result = response.json()
    print(json.dumps(result, indent=2, ensure_ascii=False))

except requests.exceptions.RequestException as e:
    print(f"请求失败: {e}")
except json.JSONDecodeError as e:
    print(f"JSON 解析失败: {e}")
except Exception as e:
    print(f"发生未知错误: {e}")